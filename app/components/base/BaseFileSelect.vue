<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  modelValue?: File[]
  multiple?: boolean
  supportFiles?: string[]
  supportFilesDisplay?: string[]
  maxFileSize?: number
  maxFileSizeDisplay?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  multiple: false,
  supportFiles: () => [],
  maxFileSize: undefined,
  maxFileSizeDisplay: ''
})

const emit = defineEmits<{
  'update:modelValue': [value: File[]]
}>()

const { t } = useI18n()
const fileInput = ref<HTMLInputElement | null>(null)
const isDragOver = ref(false)

const handleClick = () => {
  fileInput.value?.click()
}

const processFiles = (files: FileList | File[]) => {
  const newFiles: File[] = []
  const filesToProcess = props.multiple ? Array.from(files) : [files[0]]

  for (const file of filesToProcess) {
    console.log('🚀 ~ processFiles ~ file:', file?.type)

    // Check if file type is supported
    if (file && !props.supportFiles.includes(file.type)) {
      alert(t('ui.messages.pleaseSelectSupportedFile'))
      return []
    }

    // Check file size if maxFileSize is specified
    if (file && props.maxFileSize && file.size > props.maxFileSize) {
      const maxSizeDisplay = props.maxFileSizeDisplay || formatFileSize(props.maxFileSize)
      alert(t('File size exceeds maximum limit of {maxSize}', { maxSize: maxSizeDisplay }))
      return []
    }

    if (file) {
      newFiles.push(file)
    }
  }

  return newFiles
}

// Helper function to format file size
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const updateFiles = (newFiles: File[]) => {
  if (newFiles.length > 0) {
    const updatedValue = props.multiple
      ? [...props.modelValue, ...newFiles]
      : newFiles

    emit('update:modelValue', updatedValue)
  }
}

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (files && files.length > 0) {
    const newFiles = processFiles(files)
    updateFiles(newFiles)

    // Reset the input so the same file can be selected again
    target.value = ''
  }
}

// Drag and drop handlers
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false

  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    const newFiles = processFiles(files)
    updateFiles(newFiles)
  }
}
</script>

<template>
  <div
    class="relative border border-dashed border-neutral-300 dark:border-neutral-600 rounded-lg p-8 text-center cursor-pointer transition-all duration-300 hover:border-primary-400 hover:bg-neutral-50 dark:hover:bg-neutral-800/50"
    :class="{
      'border-primary-500 bg-primary-50 dark:bg-primary-900/20': isDragOver,
      'border-gray-300 dark:border-gray-600': !isDragOver
    }"
    @click="handleClick"
    @dragover="handleDragOver"
    @dragleave="handleDragLeave"
    @drop="handleDrop"
  >
    <!-- Upload Icon -->
    <div class="mb-4">
      <UIcon
        name="majesticons:cloud-upload"
        class="w-12 h-12 mx-auto text-gray-400 dark:text-gray-500"
        :class="{ 'text-primary-500': isDragOver }"
      />
    </div>

    <!-- Main Text -->
    <div class="mb-2">
      <p class="text-md font-medium text-gray-900 dark:text-gray-100">
        {{ isDragOver ? $t('ui.messages.dropFilesHere') : $t('ui.messages.dragDropOrClick') }}
      </p>
    </div>

    <!-- Secondary Text -->
    <div class="mb-4">
      <p class="text-sm text-gray-500 dark:text-gray-400">
        {{ multiple ? $t('ui.messages.selectMultipleFiles') : $t('ui.messages.selectSingleFile') }}
      </p>
    </div>

    <!-- Supported Formats and File Size Info -->
    <div class="space-y-1">
      <div
        v-if="supportFiles.length > 0"
        class="text-xs text-gray-400 dark:text-gray-500"
      >
        {{ $t('ui.messages.supportedFormats') }}: {{ supportFilesDisplay?.map(type => type.split('/')[1] || type).join(', ') }}
      </div>
      <div
        v-if="maxFileSize || maxFileSizeDisplay"
        class="text-xs text-gray-400 dark:text-gray-500"
      >
        {{ $t('Maximum file size') }}: {{ maxFileSizeDisplay || formatFileSize(maxFileSize || 0) }}
      </div>
    </div>

    <!-- Hidden File Input -->
    <input
      ref="fileInput"
      type="file"
      :accept="`application/pdf, .docx, .txt, .mobi, .epub, .pptx, .xlsx, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.openxmlformats-officedocument.presentationml.presentation, .html, .${supportFiles.join(
        ', .'
      )}`"
      :multiple="multiple"
      class="hidden"
      @change="handleFileChange"
    >

    <!-- Drag Overlay -->
    <!-- <div
      v-if="isDragOver"
      class="absolute inset-0 bg-primary-500/10 rounded-lg flex items-center justify-center"
    >
      <div class="text-primary-600 dark:text-primary-400 font-medium">
        {{ $t('ui.messages.releaseToUpload') }}
      </div>
    </div> -->
  </div>
</template>
