{"common.home": "Home", "common.back": "Back", "common.edit": "Edit", "common.save": "Save", "common.cancel": "Cancel", "common.delete": "Delete", "common.copy": "Copy", "common.copied": "<PERSON>pied", "common.manage": "Manage", "system": "AI Image Generator", "helloWorld": "Hello World!", "Describe the image you want to generate...": "Describe the image you want to generate...", "Describe the video you want to generate...": "Describe the video you want to generate...", "Describe the speech you want to generate...": "Describe the speech you want to generate...", "appTitle": "GeminiGen.AI", "copyright": "Copyright © {year}, GeminiGen.AI", "available": "Available for new projects", "notAvailable": "Not available at the moment", "blog": "Blog", "copyLink": "Copy link", "minRead": "MIN READ", "articleLinkCopied": "Article link copied to clipboard", "clickToClose": "Click anywhere or press ESC to close", "promptDetails": "Prompt details", "generateWithPrompt": "Generate with this prompt", "generateWithSettings": "Generate with these settings", "preset": "Preset", "style": "Style", "resolution": "Resolution", "addImage": "Add Image", "modelPreset": "Model/Preset", "imageDimensions": "Image Dimensions", "yourImage": "Your Image", "generate": "Generate", "cancel": "Cancel", "confirm": "Confirm", "listenToSpeech": "Listen to Speech", "generateSimilar": "Generate Similar", "Safety Filter": "Safety Filter", "personGeneration.dontAllow": "Don't Allow", "personGeneration.allowAdult": "Allow Adult", "personGeneration.allowAll": "Allow All", "safetyFilter.blockLowAndAbove": "Block Low and Above", "safetyFilter.blockMediumAndAbove": "Block Medium and Above", "safetyFilter.blockOnlyHigh": "Block Only High", "safetyFilter.blockNone": "Block None", "nav.aitool": "AI Tool", "nav.history": "History", "nav.orders": "Orders", "AI Image Generation Examples": "AI Image Generation Examples", "Explore the power of AI image generation with these interactive comparisons": "Explore the power of AI image generation with these interactive comparisons", "AI Video Generation Examples": "AI Video Generation Examples", "Explore the power of AI video generation with these interactive comparisons": "Explore the power of AI video generation with these interactive comparisons", "Try the Comparison!": "Try the Comparison!", "Try the Video Comparison!": "Try the Video Comparison!", "Drag the slider left and right to compare the before and after images. You can also click anywhere on the image to move the slider.": "Drag the slider left and right to compare the before and after images. You can also click anywhere on the image to move the slider.", "Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider.": "Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider.", "Got it!": "Got it!", "Please login to access your saved prompts.": "Please login to access your saved prompts.", "Access Your Personal Voices": "Access Your Personal Voices", "Access Your Favorite Voices": "Access Your Favorite Voices", "Login to view and manage your personal voice collection. Upload custom voices and access them anytime.": "Login to view and manage your personal voice collection. Upload custom voices and access them anytime.", "Login to view your favorite voices. Save voices you love and access them quickly for your projects.": "Login to view your favorite voices. Save voices you love and access them quickly for your projects.", "Create Account": "Create Account", "Join thousands of creators using AI voices for their projects": "Join thousands of creators using AI voices for their projects", "Duration": "Duration", "Select video duration in seconds": "Select video duration in seconds", "This setting is locked for the selected model": "This setting is locked for the selected model", "Prompts will always be refined to improve output quality (required for this model)": "Prompts will always be refined to improve output quality (required for this model)", "nav.api": "API", "nav.login": "<PERSON><PERSON>", "3D Render": "3D Render", "Acrylic": "Acrylic", "Anime General": "Anime General", "Creative": "Creative", "Dynamic": "Dynamic", "Fashion": "Fashion", "Game Concept": "Game Concept", "Graphic Design 3D": "Graphic Design 3D", "Illustration": "Illustration", "None": "None", "Portrait": "Portrait", "Portrait Cinematic": "Portrait Cinematic", "Portrait Fashion": "Portrait Fashion", "Ray Traced": "<PERSON>", "Stock Photo": "Stock Photo", "Watercolor": "Watercolor", "voice": "Voice", "emotion": "Emotion", "speed": "Speed", "speed_settings": "Speed Settings", "speed_value": "Speed Value", "speed_slider": "Speed Slider", "apply": "Apply", "speech_settings": "Speech Settings", "current_speed": "Current Speed", "reset_defaults": "Reset to Defaults", "outputFormat": "Output Format", "outputChannel": "Output Channel", "selectVoice": "Select Voice", "selectEmotion": "Select Emotion", "selectFormat": "Select Format", "selectChannel": "Select Channel", "noVoicesAvailable": "No voices available", "noEmotionsAvailable": "No emotions available", "searchVoices": "Search voices...", "searchEmotions": "Search emotions...", "noVoicesFound": "No voices found", "noEmotionsFound": "No emotions found", "retry": "Retry", "noAudioSample": "No audio sample available", "Speech Generation Complete": "Speech Generation Complete", "Your speech has been generated successfully": "Your speech has been generated successfully", "AI Image Generator": "AI Image Generator", "Generate AI images from text prompts with a magical particle transformation effect": "Generate AI images from text prompts with a magical particle transformation effect", "Enter your prompt": "Enter your prompt", "Generating...": "Generating...", "Generate Image": "Generate Image", "Enter a prompt and click Generate Image to create an AI image": "Enter a prompt and click Generate Image to create an AI image", "Prompt:": "Prompt:", "Download": "Download", "How It Works": "How It Works", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:", "Sends your prompt to an AI image generation API": "Sends your prompt to an AI image generation API", "Creates a particle system with thousands of tiny particles": "Creates a particle system with thousands of tiny particles", "Transforms the random noise particles into the generated image": "Transforms the random noise particles into the generated image", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.", "Transform": "Transform", "Transforming...": "Transforming...", "Initializing particles...": "Initializing particles...", "Loading image...": "Loading image...", "Creating particle system...": "Creating particle system...", "Adding event listeners...": "Adding event listeners...", "Ready!": "Ready!", "AI Image Particle Effect": "AI Image Particle Effect", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images", "Click anywhere or press ESC to close": "Click anywhere or press ESC to close", "auth.login": "<PERSON><PERSON>", "auth.loginDescription": "Login to your account to continue", "auth.email": "Email", "auth.enterEmail": "Enter your email", "auth.filter": "Filter", "auth.password": "Password", "auth.enterPassword": "Enter your password", "auth.rememberMe": "Remember me", "auth.welcomeBack": "Welcome back", "auth.signupFailed": "Signup failed", "auth.signupFailedDescription": "There was an error during signup. Please try again.", "auth.dontHaveAccount": "Don't have an account?", "auth.signUp": "Sign up", "auth.forgotPassword": "Forgot password?", "auth.bySigningIn": "By signing in, you agree to our", "auth.termsOfService": "Terms of Service", "auth.signUpTitle": "Sign up", "auth.signUpDescription": "Create an account to get started", "auth.name": "Name", "auth.enterName": "Enter your name", "auth.createAccount": "Create account", "auth.alreadyHaveAccount": "Already have an account?", "auth.bySigningUp": "By signing up, you agree to our", "auth.backToHome": "Back to home", "auth.notVerifyAccount": "Your account is not verified. Please verify your account to continue", "auth.verifyAccount": "Verify account", "auth.resendActivationEmail": "Resend activation email", "Account Activation Required": "Account Activation Required", "Please activate your account to access your personal voice collection. Check your email for the activation link.": "Please activate your account to access your personal voice collection. Check your email for the activation link.", "Please activate your account to access your favorite voices. Check your email for the activation link.": "Please activate your account to access your favorite voices. Check your email for the activation link.", "Resend Activation Email": "Resend Activation Email", "Check Email": "Check Email", "Didn't receive the email? Check your spam folder or try resending.": "Didn't receive the email? Check your spam folder or try resending.", "Activation email sent": "Activation email sent", "Please check your email for the activation link.": "Please check your email for the activation link.", "auth.accountRecovery": "Account Recovery", "auth.accountRecoveryTitle": "Recover your account", "auth.accountRecoveryDescription": "Enter your email to receive password reset instructions", "auth.sendRecoveryEmail": "Send recovery email", "auth.recoveryEmailSent": "Recovery email sent", "auth.recoveryEmailSentDescription": "Please check your email for password reset instructions", "auth.resetPassword": "Reset Password", "auth.resetPasswordTitle": "Reset your password", "auth.resetPasswordDescription": "Enter your new password", "auth.newPassword": "New password", "auth.confirmPassword": "Confirm password", "auth.enterNewPassword": "Enter your new password", "auth.enterConfirmPassword": "Confirm your new password", "auth.passwordResetSuccess": "Password reset successful", "auth.passwordResetSuccessDescription": "Your password has been reset successfully. You can now login with your new password", "auth.activateAccount": "Activate Account", "auth.activateAccountTitle": "Activate your account", "auth.activateAccountDescription": "Your account is being activated...", "auth.accountActivated": "Account activated", "auth.accountActivatedDescription": "Your account has been activated successfully. You can now login", "auth.activationFailed": "Activation failed", "auth.activationFailedDescription": "Failed to activate your account. Please try again or contact support", "auth.backToLogin": "Back to login", "auth.loginFailed": "<PERSON><PERSON> failed", "auth.loginWithGoogle": "Login with Google", "auth.google": "Google", "validation.invalidEmail": "Invalid email", "validation.passwordMinLength": "Password must be at least 8 characters", "validation.nameRequired": "Name is required", "validation.required": "This field is required", "validation.passwordsDoNotMatch": "Passwords do not match", "imageSelect.pleaseSelectImageFile": "Please select an image file", "imageSelect.selectedImage": "Selected image", "imageSelect.removeImage": "Remove image", "pixelReveal.loading": "Loading image...", "pixelReveal.processing": "Processing image...", "pixelReveal.revealComplete": "Image reveal complete", "SIGNIN_WRONG_EMAIL_PASSWORD": "Wrong email or password", "Try again": "Try again", "aiToolMenu.imagen": "Imagen", "aiToolMenu.videoGen": "Video Gen", "aiToolMenu.speechGen": "Speech Gen", "aiToolMenu.musicGen": "Music Gen", "aiToolMenu.textToImage": "Text to Image", "aiToolMenu.imagen3": "Imagen 3", "aiToolMenu.imagen3Description": "Generate high-quality, detailed images with accurate text rendering for creative visual content.", "aiToolMenu.imagen4": "Imagen 4", "aiToolMenu.imagen4Description": "Express your ideas like never before — with <PERSON><PERSON>, creativity has no limits.", "aiToolMenu.gemini2Flash": "Gemini 2.0 Flash", "aiToolMenu.gemini2FlashDescription": "Gemini 2.0 Flash is a powerful tool for generating images from text prompts.", "aiToolMenu.veo2": "Veo 2", "aiToolMenu.veo2Description": "Greater control, consistency, and creativity than ever before.", "aiToolMenu.veo3": "Veo 3", "aiToolMenu.veo3Description": "Video, meet audio. Our latest video generation model, designed to empower filmmakers and storytellers.", "aiToolMenu.gemini25Pro": "Gemini 2.5 Pro", "aiToolMenu.gemini25ProDescription": "The most advanced text-to-speech model available.", "aiToolMenu.gemini25Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini25FlashDescription": "Large scale processing (e.g. multiple pdfs).\nLow latency, high volume tasks which require thinking\nAgentic use cases", "aiToolMenu.link": "Link", "aiToolMenu.linkDescription": "Use NuxtLink with superpowers.", "aiToolMenu.soon": "Soon", "readArticle": "Read Article", "switchToLightMode": "Switch to light mode", "switchToDarkMode": "Switch to dark mode", "profile": "Profile", "profileSettings.emailNotifications": "Email Notifications", "profileSettings.marketingEmails": "Marketing Emails", "profileSettings.securityAlerts": "Security Alerts", "buyCredits.checkout": "Checkout", "buyCredits.checkoutDescription": "Confirm your order then choose your payment method.", "buyCredits.orderDetail": "Order detail", "buyCredits.credits": "Credits", "buyCredits.pricePerUnit": "Price per unit", "buyCredits.totalCredits": "Total credits", "buyCredits.totalPrice": "Total price", "buyCredits.payment": "Payment", "buyCredits.submit": "Submit", "buyCredits.cancel": "Cancel", "pricing.title": "Pricing", "pricing.description": "Choose the perfect plan for your image generation needs", "pricing.comingSoon": "Coming Soon", "pricing.comingSoonDescription": "Our pricing plans are being finalized. Check back soon for updates.", "Budget Calculator": "Budget Calculator", "Resource Calculator": "Resource Calculator", "Budget Amount": "Budget Amount", "Resources you can generate:": "Resources you can generate:", "Select resources you want:": "Select resources you want:", "credits": "credits", "image": "image", "video": "video", "per item": "per item", "Quantity": "Quantity", "Total Cost:": "Total Cost:", "Approximately {credits} credits": "Approximately {credits} credits", "Images": "Images", "Videos": "Videos", "Pricing Calculator": "Pricing Calculator", "Calculate how many resources can you generate with your budget.": "Calculate how many resources can you generate with your budget.", "Minimum $10 required": "Minimum $10 required", "Minimum Purchase Required": "Minimum Purchase Required", "Minimum purchase amount is $10. Please increase your selection.": "Minimum purchase amount is $10. Please increase your selection.", "Minimum purchase amount is $10": "Minimum purchase amount is $10", "Please add more resources to reach the minimum purchase amount.": "Please add more resources to reach the minimum purchase amount.", "Enter exact number": "Enter exact number", "Enter budget amount": "Enter budget amount", "Min: $10": "Min: $10", "Each amount shows what you can generate with your entire budget (choose one type)": "Each amount shows what you can generate with your entire budget (choose one type)", "OR": "OR", "magicImageDemo.title": "AI Image Particle Effect", "magicImageDemo.description": "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images", "magicImageDemo.image": "Image", "magicImageDemo.aboutTitle": "About This Component", "magicImageDemo.aboutDescription": "The BaseMagicImage component uses Three.js to create a particle system that can transform between random positions and an AI-generated image. The particles move with swirling and flowing effects, creating a magical transformation.", "magicImageDemo.featuresTitle": "Features", "magicImageDemo.features.particleRendering": "Particle-based image rendering", "magicImageDemo.features.smoothTransitions": "Smooth transitions between random particle positions and image formation", "magicImageDemo.features.interactiveControls": "Interactive camera controls (drag to rotate, scroll to zoom)", "magicImageDemo.features.customizable": "Customizable particle count and animation duration", "magicImageDemo.features.automatic": "Automatic or manual transformation triggering", "magicImageDemo.howItWorksTitle": "How It Works", "magicImageDemo.howItWorksDescription": "The component analyzes the pixels of an image and creates a 3D particle system where each particle represents a pixel. Brighter pixels are positioned closer to the viewer, creating a subtle 3D effect. The particles are initially scattered randomly in 3D space, then animate to form the image when triggered.", "privacy.title": "Privacy Policy", "privacy.description": "Learn how we protect your privacy and handle your data", "privacy.lastUpdated": "Last Updated:", "privacy.lastUpdatedDate": "January 15, 2025", "privacy.introduction": "At GeminiGen.AI, we prioritize the protection of your privacy and the security of your personal information. This privacy policy outlines how we collect, utilize, and safeguard the information you provide when using our AI content generation services including image generation, video generation, speech synthesis, and dialogue generation. By accessing and using our website (geminigen.ai), you consent to the practices described in this policy.", "privacy.informationWeCollect": "1. Information Collection", "privacy.informationCollectionDescription": "When you create an account on our website, we collect certain personal information such as your email address and complete name. This information is necessary to grant you access to our services, provide updates or changes to our services, and for statistical analysis to enhance our offerings. Additionally, any text, images, or documents uploaded for AI content generation are stored temporarily solely for the purpose of generating the output.", "privacy.creditCalculation": "2. Credit Calculation", "privacy.creditCalculationDescription": "To ensure accurate billing, the number of credits required for AI content generation is calculated based on the text, images, or documents provided. This calculation is performed using our proprietary algorithm and is directly proportional to the complexity and length of the input.", "privacy.paymentSecurity": "3. Payment & Security", "privacy.paymentSecurityDescription": "For payment processing, we offer PayPal and credit card options. We do not store credit card information on our servers. All payment transactions are securely handled by trusted third-party payment service providers in compliance with their respective privacy and security policies.", "privacy.emailNotification": "4. Email Notification and Access to Generated Content", "privacy.emailNotificationDescription": "Upon completion of content generation, you will receive an email notification containing a secure link to access and download the generated output (images, videos, audio files, or dialogue). This link remains active for a specified period of time for your convenience.", "privacy.dataSecurity": "5. Data Security", "privacy.dataSecurityDescription": "We implement industry-standard security measures to protect your personal information and uploaded documents from unauthorized access, disclosure, or alteration. While we strive to maintain the highest level of security, please note that no method of transmission over the internet or electronic storage is entirely secure.", "privacy.thirdPartyServices": "6. Third-Party Services", "privacy.thirdPartyServicesDescription": "We may utilize third-party services, such as analytics providers, to enhance our services and analyze usage patterns. These services may collect information about your usage but do not have access to your personal information.", "privacy.cookies": "7. Cookies and Tracking Technologies", "privacy.cookiesDescription": "Our website employs cookies and similar tracking technologies to improve user experience and analyze website usage. You have the option to disable cookies through your browser settings, but please be aware that some features of our website may not function properly as a result.", "privacy.thirdPartyLinks": "8. Third-Party Links", "privacy.thirdPartyLinksDescription": "Our website may contain links to third-party websites. We are not responsible for the privacy practices or content of these websites and encourage you to review their respective privacy policies.", "privacy.childrenPrivacy": "9. Children's Privacy", "privacy.childrenPrivacyDescription": "Our services are not intended for individuals under the age of 18, and we do not knowingly collect or store personal information from anyone under this age. If we become aware of inadvertent collection of personal information from a child under 18, we will take steps to remove such information from our records.", "privacy.policyChanges": "10. Updates to our Privacy Policy", "privacy.policyChangesDescription": "We may periodically update our Privacy Policy to reflect changes in practices or legal requirements. Any revisions will be effective immediately upon posting the updated policy on our website. We encourage you to review this Privacy Policy periodically for the latest information.", "privacy.commercialUse": "11. Commercial Use", "privacy.commercialUseDescription": "You have the right to use the content generated by our services for personal, educational, or commercial purposes. However, you may not resell, redistribute, or sublicense the generated content without prior written consent from GeminiGen.AI.", "privacy.otherPeoplePrivacy": "12. Other people's privacy", "privacy.otherPeoplePrivacyDescription": "You must respect the privacy of others when using our services. Do not upload or create content containing personal information, confidential data, or copyrighted material without permission.", "privacy.unsubscribe": "13. Unsubscribe", "privacy.unsubscribeDescription": "You can opt out of targeted advertising by clicking on the 'Toggle' button in your profile settings.", "privacy.contactUs": "14. Contact Us", "terms.title": "Terms of Service", "terms.description": "This Terms of Service is meant to help you understand what information we collect, why we collect it, and how you can update, manage, export, and delete your information.", "terms.lastUpdated": "Last Updated:", "terms.lastUpdatedDate": "January 15, 2025", "terms.introduction": "Welcome to GeminiGen.AI. These Terms of Service govern your use of our AI-powered content generation services, including image generation, video generation, speech synthesis, and dialogue generation.", "terms.acceptanceOfTerms": "1. Terms Acceptance", "terms.acceptanceOfTermsDescription": "By utilizing the GeminiGen.AI services, you agree to adhere to these Terms of Service. If you disagree with any part of these terms, please refrain from using our services.", "terms.acceptanceOfTermsDetails": "Your continued use of our services constitutes acceptance of any changes to these Terms.", "terms.serviceDescription": "2. Description of Services", "terms.serviceDescriptionText": "GeminiGen.AI provides AI-powered content generation services, including but not limited to:", "terms.useOfService": "4. Service Usage", "terms.serviceUsageDescription": "You undertake to employ our services solely for lawful purposes. You shall refrain from uploading, transmitting, or storing any content that is unlawful, harmful, defamatory, or infringes upon the rights of others. You are solely liable for any content submitted for AI content generation.", "terms.permittedUse": "4.1 Permitted Use", "terms.permitted1": "Use our services for lawful, creative, and commercial purposes", "terms.permitted2": "Generate content that complies with applicable laws and regulations", "terms.permitted3": "Respect intellectual property rights of others", "terms.permitted4": "Use generated content in accordance with our licensing terms", "terms.prohibitedUse": "4.2 Prohibited Use", "terms.prohibited1": "Generate content that is illegal, harmful, threatening, abusive, or discriminatory", "terms.prohibited2": "Create content that violates intellectual property rights of others", "terms.prohibited3": "Produce content intended to deceive, defraud, or mislead others", "terms.prohibited4": "Generate content depicting minors in inappropriate situations", "terms.prohibited5": "Create content that promotes violence, terrorism, or illegal activities", "terms.prohibited6": "Use our services to spam, harass, or harm others", "terms.prohibited7": "Attempt to reverse engineer, hack, or compromise our systems", "terms.prohibited8": "Violate any applicable laws or regulations", "terms.userAccounts": "2. User Accounts", "terms.userAccountsDescription": "Accessing certain features of our service may necessitate the creation of a user account. You commit to maintaining the confidentiality of your account credentials and accept sole responsibility for all activities conducted under your account.", "terms.userAccounts1": "Providing accurate and complete registration information", "terms.userAccounts2": "Maintaining the security and confidentiality of your account credentials", "terms.userAccounts3": "All activities that occur under your account", "terms.userAccounts4": "Notifying us immediately of any unauthorized use of your account", "terms.userAccounts5": "Ensuring your account information remains current and accurate", "terms.paymentAndBilling": "3. Payment and Credits", "terms.paymentAndBillingDescription": "Our AI content generation services operate on a credit-based system. The number of credits required for content creation is determined by our proprietary algorithm and is accurately calculated based on the input complexity and output requirements.", "terms.payment1": "Upon depletion of your credit balance, you must recharge your account", "terms.payment2": "Payments can be made via PayPal or credit card", "terms.payment3": "All payments are processed securely through third-party payment processors", "terms.payment4": "Credits are non-refundable except as required by applicable law", "terms.payment5": "Prices are subject to change with reasonable notice", "terms.payment6": "You are responsible for all applicable taxes and fees", "terms.intellectualProperty": "5. Intellectual Property", "terms.intellectualPropertyDescription": "All content and materials accessible through our service, encompassing text, graphics, logos, and software, are the property of GeminiGen.AI and are safeguarded by intellectual property laws. Reproduction, modification, distribution, or creation of derivative works of the content without prior written consent is prohibited.", "terms.ourIntellectualProperty": "5.1 Our Intellectual Property", "terms.ourIntellectualPropertyDescription": "GeminiGen.AI and its services, including all software, algorithms, designs, and content, are protected by intellectual property laws. You may not copy, modify, distribute, or create derivative works without our express written permission.", "terms.userGeneratedContent": "5.2 User-Generated Content", "terms.userGeneratedContentDescription": "You retain ownership of content you create using our services, subject to the following:", "terms.userContent1": "You grant us a limited license to process and store your content to provide our services", "terms.userContent2": "You represent that you have the right to use any input content you provide", "terms.userContent3": "You are responsible for ensuring your generated content complies with these Terms", "terms.userContent4": "We may remove content that violates our policies or applicable laws", "terms.privacyAndDataProtection": "7. Privacy and Data Protection", "terms.privacyAndDataProtectionDescription": "Your privacy is important to us. Our collection, use, and protection of your personal information is governed by our Privacy Policy, which is incorporated into these Terms by reference.", "terms.serviceAvailability": "8. Service Availability", "terms.serviceAvailabilityDescription": "While we strive to provide reliable services, we do not guarantee uninterrupted access. Our services may be temporarily unavailable due to maintenance, updates, or technical issues. We reserve the right to modify or discontinue services with reasonable notice.", "terms.termination": "8. Termination", "terms.terminationDescription": "We retain the right to suspend or terminate your account and access to our services at our discretion, with or without cause. Upon termination, your account and associated data will be deleted, except for information mandated to be retained for legal or accounting purposes.", "terms.terminationByUser": "8.1 Termination by You", "terms.terminationByUserDescription": "You may terminate your account at any time by contacting our support team. Upon termination, your access to our services will cease, but these Terms will continue to apply to your prior use of our services.", "terms.terminationByUs": "8.2 Termination by Us", "terms.terminationByUsDescription": "We may suspend or terminate your account and access to our services immediately, with or without prior notice, for any of the following reasons:", "terms.termination1": "Violation of these Terms of Service", "terms.termination2": "Fraudulent, abusive, or illegal activity", "terms.termination3": "Non-payment of fees or charges", "terms.termination4": "Extended periods of inactivity", "terms.termination5": "Legal or regulatory requirements", "terms.termination6": "Protection of our rights, property, or safety", "terms.limitationOfLiability": "6. Limitation of Liability", "terms.limitationOfLiabilityDescription": "GeminiGen.AI shall not be held liable for any direct, indirect, incidental, special, or consequential damages arising from or related to your use of our services. We do not warrant the accuracy, completeness, or availability of the services and disclaim all warranties, whether express or implied, regarding their use or outcomes.", "terms.disclaimers": "7. <PERSON><PERSON><PERSON>", "terms.disclaimersDescription": "Our services are provided 'as is' and 'as available' without warranties of any kind. We specifically disclaim:", "terms.disclaimer1": "Warranties of merchantability, fitness for a particular purpose, and non-infringement", "terms.disclaimer2": "Guarantees regarding the accuracy, reliability, or quality of generated content", "terms.disclaimer3": "Responsibility for how you use or distribute generated content", "terms.disclaimer4": "Liability for any damages resulting from service interruptions or technical issues", "terms.indemnification": "12. Indemnification", "terms.indemnificationDescription": "You agree to indemnify, defend, and hold harmless GeminiGen.AI and its affiliates from any claims, damages, losses, or expenses arising from your use of our services, violation of these Terms, or infringement of any third-party rights.", "terms.governingLaw": "9. Governing Law", "terms.governingLawDescription": "These Terms of Service shall be interpreted and governed in accordance with the laws of Viet Nam, without regard to its conflict of law principles. Any disputes arising from or relating to these Terms and the utilization of our services shall be subject to the exclusive jurisdiction of the courts in Viet Nam.", "terms.clarificationOpenAI": "10. Clarification Regarding Third-Party AI Services", "terms.clarificationOpenAIDescription": "GeminiGen.AI is an independent entity and is not affiliated with OpenAI, Google, or other AI service providers. Our content generation services utilize various AI APIs to convert text into images, videos, speech, and dialogue, but we operate independently from these providers. This clarification is made to prevent any confusion or misunderstanding regarding the relationship between GeminiGen.AI and third-party AI service providers. Users should be aware that while we use third-party AI technology to provide our services, GeminiGen.AI is solely responsible for the operation of our services and the adherence to these Terms of Service.", "terms.contactUsTerms": "11. Contact Information", "terms.contactUsTermsDescription": "If you have any questions about these Terms of Service or need to report a violation, please contact us:", "terms.contactEmail": "Email:", "terms.contactAddress": "Address:", "terms.companyAddress": "The website is jointly operated by A2ZAI LTD No:******** Registered address at 483 Green Lanes, London, England, N13 4BS", "footer.supportEmail": "Email support", "footer.address": "Address", "footer.companyAddress": "The website is jointly operated by A2ZAI LTD No:******** Registered address at 483 Green Lanes, London, England, N13 4BS", "appName": "GeminiGen.AI", "quickTopUp": "Quick top up", "customTopUp": "Custom top up", "numberOfCredits": "Number of credits", "paypal": "PayPal", "paypalDescription": "Pay securely with your PayPal account", "stripe": "Stripe", "stripeDescription": "Pay securely with <PERSON>e", "debitCreditCard": "Debit or Credit Card", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "Pay with Crypto", "cryptoDescription": "Bitcoin, Ethereum, and other cryptocurrencies", "profileMenu.guide": "Guide", "profileMenu.logo": "Logo", "profileMenu.settings": "Settings", "profileMenu.integration": "Integration", "profileMenu.components": "Components", "loadingMoreItems": "Loading more items...", "history.tabs.imagen": "Imagen", "history.tabs.video": "Video", "history.tabs.speech": "Speech", "history.tabs.music": "Music", "history.tabs.history": "History", "orders.title": "Order History", "orders.description": "View your transaction and payment history", "orders.orderId": "Order ID", "orders.amount": "Amount", "orders.credits": "Credits", "orders.quantity": "Quantity", "orders.platform": "Platform", "orders.externalId": "Transaction ID", "orders.status.completed": "Completed", "orders.status.success": "Success", "orders.status.paid": "Paid", "orders.status.pending": "Pending", "orders.status.processing": "Processing", "orders.status.failed": "Failed", "orders.status.cancelled": "Cancelled", "orders.status.error": "Error", "orders.empty.title": "No orders yet", "orders.empty.description": "You haven't made any orders yet. Buy credits to start using our services.", "orders.empty.action": "Buy Credits", "orders.endOfList": "You've seen all orders", "orders.errors.fetchFailed": "Failed to load order history. Please try again.", "orders.meta.title": "Order History - Imagen AI", "orders.meta.description": "View your transaction and payment history on Imagen AI", "promptLabel": "Prompt:", "videoExamples": "Video Examples", "videoExamplesDescription": "Explore these video examples with their prompts and settings. Click on any 'Use This Prompt' button to copy the prompt to your input field.", "useThisPrompt": "Use This Prompt", "model": "Model", "duration": "Duration", "videoTypeSelection": "Select Video Type", "videoTypes.examples.tikTokDanceTrend": "TikTok Dance Trend", "videoTypes.examples.energeticDanceDescription": "Energetic dance video with vibrant colors, quick cuts, trending music, vertical format, social media style", "videoTypes.examples.instagramReel": "Instagram Reel", "videoTypes.examples.lifestyleDescription": "Lifestyle content with aesthetic visuals, smooth transitions, trendy effects, engaging storytelling", "videoTypes.examples.comedySketch": "Comedy Sketch", "videoTypes.examples.funnyComedyDescription": "Funny comedy scene with expressive characters, humorous situations, entertaining dialogue, lighthearted mood", "videoTypes.examples.productLaunchAd": "Product Launch Ad", "videoTypes.examples.professionalCorporateDescription": "Professional corporate video with executive presentation, clean office environment, business formal style", "videoTypes.examples.quickPromoVideo": "Quick Promo Video", "videoTypes.examples.fastPacedPromoDescription": "Fast-paced promotional content with efficient production, cost-effective visuals, streamlined messaging", "videoTypes.examples.birthdayGreeting": "Birthday Greeting", "videoTypes.examples.personalizedBirthdayDescription": "Personalized birthday video with festive decorations, warm lighting, celebratory atmosphere, heartfelt message", "videoTypes.examples.brandStoryVideo": "Brand Story Video", "videoTypes.examples.tutorialVideo": "Tutorial Video", "videoTypes.examples.manOnThePhone": "Man on the phone", "videoTypes.examples.runningSnowLeopard": "Running snow leopard", "videoTypes.examples.snowLeopard": "Snow leopard", "videoTypes.styles.cartoonAnimated": "Cartoon or animated style video", "videoTypes.styles.naturalDocumentary": "Natural documentary-style footage", "videoTypes.styles.naturalLifelike": "Natural, lifelike video style", "videoTypes.styles.professionalMovieQuality": "Professional movie-like quality with dramatic lighting", "videoTypes.styles.creativeStylized": "Creative and stylized video effects", "videoTypes.styles.retroVintage": "Retro or vintage video aesthetic", "notifications.title": "Notifications", "notifications.description": "Your recent notifications and updates", "notifications.totalCount": "{count} notifications", "notifications.markAllRead": "Mark all as read", "notifications.loadMore": "Load more", "notifications.close": "Close", "notifications.empty.title": "No notifications", "notifications.empty.description": "You're all caught up! No new notifications to show.", "notifications.error.title": "Error loading notifications", "notifications.time.justNow": "Just now", "notifications.time.minutesAgo": "{minutes}m ago", "notifications.time.hoursAgo": "{hours}h ago", "notifications.time.yesterday": "Yesterday", "notifications.status.processing.title": "Processing", "notifications.status.processing.description": "Your request is being processed", "notifications.status.success.title": "Completed", "notifications.status.success.description": "Successfully completed", "notifications.status.failed.title": "Failed", "notifications.status.failed.description": "An error occurred during processing", "notifications.status.warning.title": "Warning", "notifications.status.warning.description": "Completed with warnings", "notifications.status.pending.title": "Pending", "notifications.status.pending.description": "Waiting to be processed", "notifications.status.cancelled.title": "Cancelled", "notifications.status.cancelled.description": "Request was cancelled", "notifications.types.video_1.title": "Video Generation Pending", "notifications.types.video_1.description": "Video generation is waiting to be processed", "notifications.types.video_2.title": "Video Generation Complete", "notifications.types.video_2.description": "Video has been generated successfully", "notifications.types.video_3.title": "Video Generation Failed", "notifications.types.video_3.description": "Video generation failed", "notifications.types.image_1.title": "Image Generation Pending", "notifications.types.image_1.description": "Image generation is waiting to be processed", "notifications.types.image_2.title": "Image Generation Complete", "notifications.types.image_2.description": "Image has been generated successfully", "notifications.types.image_3.title": "Image Generation Failed", "notifications.types.image_3.description": "Image generation failed", "notifications.types.tts_history_1.title": "Audio Generation Pending", "notifications.types.tts_history_1.description": "Text-to-speech is waiting to be processed", "notifications.types.tts_history_2.title": "Audio Generation Complete", "notifications.types.tts_history_2.description": "Text-to-speech audio has been generated successfully", "notifications.types.tts_history_3.title": "Audio Generation Failed", "notifications.types.tts_history_3.description": "Text-to-speech generation failed", "notifications.types.voice_training_1.title": "Voice Training Pending", "notifications.types.voice_training_1.description": "Voice training is waiting to be processed", "notifications.types.voice_training_2.title": "Voice Training Complete", "notifications.types.voice_training_2.description": "Custom voice model training has finished successfully", "notifications.types.voice_training_3.title": "Voice Training Failed", "notifications.types.voice_training_3.description": "Voice training failed", "notifications.types.music_1.title": "Music Generation Pending", "notifications.types.music_1.description": "Music generation is waiting to be processed", "notifications.types.music_2.title": "Music Generation Complete", "notifications.types.music_2.description": "AI music has been generated successfully", "notifications.types.music_3.title": "Music Generation Failed", "notifications.types.music_3.description": "Music generation failed", "notifications.types.speech_1.title": "Speech Generation Pending", "notifications.types.speech_1.description": "Your speech generation request is waiting to be processed", "notifications.types.speech_2.title": "Speech Generation Complete", "notifications.types.speech_2.description": "Your speech has been generated successfully", "notifications.types.speech_3.title": "Speech Generation Failed", "notifications.types.speech_3.description": "Your speech generation failed. Please try again", "notifications.types.default.title": "Notification", "notifications.types.default.description": "You have a new notification", "footer.nuxtUIOnDiscord": "Nuxt UI on Discord", "historyFilter.all": "All", "historyFilter.imagen": "Imagen", "historyFilter.videoGen": "Video Gen", "historyFilter.speechGen": "Speech Gen", "historyFilter.dialogueGen": "Dialogue Gen", "historyFilter.speechGenDocument": "Speech Gen from Document", "historyPages.imagenDescription": "Browse your AI-generated images and artwork", "historyPages.musicDescription": "Browse your AI-generated music and audio content", "historyPages.speechDescription": "Browse your AI-generated speech and voice content", "historyPages.videoDescription": "Browse your AI-generated videos and animations", "historyPages.imagenBreadcrumb": "Imagen", "historyPages.musicBreadcrumb": "Music", "historyPages.speechBreadcrumb": "Speech", "historyPages.videoBreadcrumb": "Video Generation", "historyPages.endOfImagesHistory": "You've reached the end of the images history", "historyPages.endOfHistory": "You've reached the end of the history", "historyPages.endOfMusicHistory": "You've reached the end of the music history", "historyPages.endOfSpeechHistory": "You've reached the end of the speech history", "historyPages.endOfVideoHistory": "You've reached the end of the video history", "historyPages.noVideosFound": "No videos found", "historyPages.noVideosFoundDescription": "Start generating videos to see them here.", "historyPages.backToLibrary": "Back to Library", "historyPages.errorLoadingVideo": "Error Loading Video", "historyPages.loadingVideoDetails": "Loading video details...", "historyPages.videoDetails": "Video Details", "historyPages.videoInformation": "Video Information", "historyPages.videoNotFound": "The video you are looking for could not be found or loaded.", "historyPages.aiContentLibraryTitle": "AI Content Library", "historyPages.aiContentLibraryDescription": "Browse and manage your AI-generated content across different categories", "demo.notifications.title": "Notification Types & Status Demo", "demo.notifications.description": "Examples of different notification types with various status states", "demo.notifications.statusLegend": "Status Legend", "demo.notifications.availableNotificationTypes": "Available Notification Types", "demo.speechVoiceSelect.title": "Speech Voice Select Demo", "demo.speechVoiceSelect.description": "Demonstrating the reusable BaseSpeechVoiceSelectModal component with modelValue props", "demo.speechVoiceSelect.example1": "Example 1: <PERSON><PERSON><PERSON>", "demo.speechVoiceSelect.example2": "Example 2: Small Size", "demo.speechVoiceSelect.example3": "Example 3: Large Size", "demo.speechVoiceSelect.example4": "Example 4: Multiple Error Examples", "demo.speechVoiceSelect.example5": "Example 5: Status Comparison", "demo.speechVoiceSelect.mainNarrator": "Main Narrator", "demo.speechVoiceSelect.characterVoice": "Character Voice", "demo.speechVoiceSelect.selectedVoicesSummary": "Selected Voices Summary:", "demo.speechVoiceSelect.clearAll": "Clear All", "demo.speechVoiceSelect.setRandomVoices": "Set Random Voices", "demo.speechVoiceSelect.logToConsole": "Log to Console", "demo.speechVoiceSelect.notSelected": "Not selected", "demo.speechVoiceSelect.voiceSelectionsChanged": "Voice selections changed", "demo.historyWrapper.title": "HistoryWrapper Status Badge Demo", "demo.historyWrapper.normalStatus": "Example 1: Normal Status (status = 1)", "demo.historyWrapper.processingStatus": "Example 2: Processing Status (status = 2)", "demo.historyWrapper.errorStatus": "Example 3: Error Status (status = 3) - Shows Error Badge", "demo.historyWrapper.multipleErrorExamples": "Example 4: Multiple Error Examples", "demo.historyWrapper.statusComparison": "Example 5: Status Comparison", "demo.historyWrapper.normalImageGeneration": "Normal Image Generation", "demo.historyWrapper.videoGenerationInProgress": "Video Generation in Progress", "demo.historyWrapper.speechGenerationFailed": "Speech Generation Failed", "demo.historyWrapper.imageFailed": "Image Failed", "demo.historyWrapper.videoFailed": "Video Failed", "demo.historyWrapper.speechFailed": "Speech Failed", "demo.historyWrapper.statusSuccess": "Status: Success", "demo.historyWrapper.statusProcessing": "Status: Processing", "demo.historyWrapper.statusError": "Status: Error", "demo.historyWrapper.status1Success": "Status 1: Success", "demo.historyWrapper.status2Processing": "Status 2: Processing", "demo.historyWrapper.badgeBehavior": "Badge Behavior:", "demo.historyWrapper.showsOnlyTypeAndStyle": "Shows only type and style badges", "demo.historyWrapper.showsTypeStyleAndError": "Shows type, style, and red error badge with alert icon", "demo.historyWrapper.redBackgroundWithWhite": "Red background with white text and alert circle icon", "demo.historyWrapper.allBadgesHideOnHover": "All badges hide on hover to show overlay content", "demo.speechVoiceCaching.title": "Speech Voice Caching Test", "demo.speechVoiceCaching.description": "Test để kiểm tra việc cache voices giữa các components khác nhau", "demo.speechVoiceCaching.component1Modal": "Component 1 - Modal", "demo.speechVoiceCaching.component3RegularSelect": "Component 3 - Regular Select", "demo.speechVoiceCaching.forceReloadVoices": "Force Reload Voices", "demo.speechVoiceCaching.clearAllSelections": "Clear All Selections", "demo.speechVoiceCaching.logStoreState": "Log Store State", "demo.speechVoiceCaching.refreshPageInstructions": "Refresh page và mở bất kỳ component nào - sẽ load lại", "demo.speechVoiceCaching.checkNetworkTab": "Kiểm tra Network tab để xác nhận API calls", "demo.speechVoiceCaching.selectedVoicePersist": "Selected voice sẽ đ<PERSON><PERSON><PERSON> persist qua localStorage", "demo.speechVoiceCaching.pageMounted": "Page mounted, store will auto-initialize if needed", "settings": "Settings", "userMenu.profile": "Profile", "userMenu.buyCredits": "Buy Credits", "userMenu.settings": "Settings", "userMenu.api": "API", "userMenu.logout": "Logout", "userMenu.greeting": "Hi, {name}", "integration.title": "Integration", "integration.subtitle": "Manage your API keys and integration settings", "integration.apiKeys": "API Keys", "integration.apiKeysDescription": "Manage your API keys for programmatic access", "integration.webhook": "Webhook", "integration.webhookDescription": "Configure webhook URL for notifications", "apiKeys.title": "API Keys", "apiKeys.subtitle": "Manage your API keys for programmatic access", "apiKeys.create": "Create API Key", "apiKeys.createNew": "Create New API Key", "apiKeys.createFirst": "Create First API Key", "apiKeys.name": "Name", "apiKeys.nameDescription": "Give your API key a descriptive name", "apiKeys.namePlaceholder": "e.g., My App API Key", "apiKeys.nameRequired": "API key name is required", "apiKeys.createdAt": "Created", "apiKeys.noKeys": "No API Keys", "apiKeys.noKeysDescription": "Create your first API key to get started with programmatic access", "apiKeys.created": "API key created successfully", "apiKeys.createError": "Failed to create API key", "apiKeys.deleted": "API key deleted successfully", "apiKeys.deleteError": "Failed to delete API key", "apiKeys.deleteConfirm": "Delete API Key", "apiKeys.deleteWarning": "Are you sure you want to delete this API key? This action cannot be undone.", "apiKeys.copied": "API key copied to clipboard", "apiKeys.copyError": "Failed to copy API key", "apiKeys.successTitle": "API Key Created Successfully", "apiKeys.importantNotice": "Important Notice", "apiKeys.copyWarning": "This is the only time you will be able to view and copy this API key. Please copy it now and store it securely.", "apiKeys.key": "API Key", "apiKeys.securityTip": "Security Tips:", "apiKeys.tip1": "Store this key in a secure location", "apiKeys.tip2": "Never share your API key publicly", "apiKeys.tip3": "If compromised, delete this key and create a new one", "apiKeys.copyFirst": "Copy API Key First", "common.done": "Done", "webhook.title": "Webhook Configuration", "webhook.subtitle": "Configure webhook URL for real-time notifications", "webhook.configuration": "Webhook URL", "webhook.currentUrl": "Current Webhook URL", "webhook.currentUrlDescription": "This URL will receive POST requests for webhook events", "webhook.notConfigured": "No webhook URL configured", "webhook.url": "Webhook URL", "webhook.urlDescription": "Enter the URL where you want to receive webhook notifications", "webhook.urlPlaceholder": "https://your-domain.com/webhook", "webhook.urlRequired": "Please enter a webhook URL first", "webhook.invalidUrl": "Please enter a valid URL", "webhook.saved": "Webhook URL saved successfully", "webhook.saveError": "Failed to save webhook URL", "webhook.test": "Test", "webhook.testSent": "Test Sent", "webhook.testDescription": "Test webhook sent successfully", "webhook.information": "Webhook Information", "webhook.howItWorks": "How it works", "webhook.description": "When configured, we will send HTTP POST requests to your webhook URL whenever certain events occur in your account.", "webhook.events": "Webhook Events", "webhook.imageGenerated": "Image generation completed", "webhook.imageGenerationFailed": "Image generation failed", "webhook.creditUpdated": "Credit balance updated", "webhook.payloadFormat": "Payload Format", "webhook.payloadDescription": "Webhook requests will be sent as JSON with the following structure:", "webhook.security": "Security", "webhook.securityDescription": "We recommend using HTTPS URLs and implementing signature verification to ensure webhook authenticity.", "error.general": "Error", "error.validation": "Validation Error", "error.required": "Required field", "success.saved": "Saved successfully", "success.created": "Created successfully", "success.deleted": "Deleted successfully", "success.copied": "Copied to clipboard", "confirmDelete": "Confirm Delete", "confirmDeleteDescription": "Are you sure you want to delete this item? This action cannot be undone.", "historyDeleted": "History item deleted successfully", "deleteError": "Failed to delete history item", "Regenerate Image": "Regenerate Image", "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?": "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?", "Yes, Regenerate": "Yes, <PERSON><PERSON><PERSON>", "Cancel": "Cancel", "models.imagen4Fast": "Imagen 4 Fast", "models.imagen4Ultra": "Imagen 4 Ultra", "formats.mp3": "MP3", "formats.wav": "WAV", "channels.mono": "Mono", "channels.stereo": "Stereo", "options.allow": "Allow", "options.dontAllow": "Don't Allow", "options.voices": "Voices", "options.pickVoice": "Pick a voice", "voiceTypes.favoriteVoices": "Favorite Voices", "voiceTypes.geminiVoices": "Gemini Voices", "voiceTypes.systemVoices": "System Voices", "voiceTypes.customVoices": "Custom Voices", "voiceTypes.premiumVoices": "Premium Voices", "voiceTypes.userVoices": "User Voices", "aspectRatio": "Aspect Ratio", "Image Reference": "Image Reference", "Person Generation": "Person Generation", "safety_filter_level": "Safety Filter Level", "used_credit": "Used Credit", "downloadImage": "Download Image", "noImageAvailable": "No image available", "enhancePrompt": "Enhance Prompt", "addImages": "Add Images", "generateVideo": "Generate Video", "happy": "Happy", "sad": "Sad", "angry": "Angry", "excited": "Excited", "laughing": "Laughing", "crying": "Crying", "calm": "Calm", "serious": "Serious", "frustrated": "Frustrated", "hopeful": "Hopeful", "narrative": "Narrative", "kids' storytelling": "Kids' Storytelling", "audiobook": "Audiobook", "poetic": "Poetic", "mysterious": "Mysterious", "inspirational": "Inspirational", "surprised": "Surprised", "confident": "Confident", "romantic": "Romantic", "scared": "Scared", "trailer voice": "Trailer Voice", "advertising": "Advertising", "speech.dialogueGeneration.complete": "Dialogue Generation Complete", "speech.dialogueGeneration.failed": "Dialogue Generation Failed", "speech.dialogueGeneration.pending": "Dialogue Generation Pending", "speech.dialogueGeneration.dialogueGen": "Dialogue Gen", "speech.dialogueGeneration.successMessage": "Your dialogue has been generated successfully", "speech.speechGeneration.complete": "Speech Generation Complete", "speech.speechGeneration.failed": "Speech Generation Failed", "speech.speechGeneration.pending": "Speech Generation Pending", "speech.speechGeneration.successMessage": "Your speech has been generated successfully", "speech.speechGeneration.requestWaiting": "Your speech generation request is waiting to be processed", "speech.errors.failedToLoadEmotions": "Failed to load emotions", "documentary": "Documentary", "newsreader": "Newsreader", "weather report": "Weather Report", "game commentary": "Game Commentary", "interactive": "Interactive", "customer support": "Customer Support", "playful": "Playful", "tired": "Tired", "sarcastic": "Sarcastic", "disgusted": "Disgusted", "whispering": "Whispering", "persuasive": "Persuasive", "nostalgic": "Nostalgic", "meditative": "Meditative", "announcement": "Announcement", "professional pitch": "Professional Pitch", "casual": "Casual", "exciting trailer": "Exciting Trailer", "dramatic": "Dramatic", "corporate": "Corporate", "tech enthusiast": "Tech Enthusiast", "youthful": "Youthful", "calming reassurance": "Calming Reassurance", "heroic": "Heroic", "festive": "Festive", "urgent": "<PERSON><PERSON>", "motivational": "Motivational", "friendly": "Friendly", "energetic": "Energetic", "serene": "<PERSON><PERSON>", "bold": "Bold", "charming": "<PERSON><PERSON>", "monotone": "Monotone", "questioning": "Questioning", "directive": "Directive", "dreamy": "<PERSON>y", "epic": "Epic", "lyrical": "Lyrical", "mystical": "Mystical", "melancholy": "<PERSON><PERSON><PERSON><PERSON>", "cheerful": "Cheerful", "eerie": "Eerie", "flirtatious": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thoughtful": "Thoughtful", "cinematic": "Cinematic", "humorous": "Humorous", "instructional": "Instructional", "conversational": "Conversational", "apologetic": "Apologetic", "excuse-making": "Excuse-making", "encouraging": "Encouraging", "neutral": "Neutral", "authoritative": "Authoritative", "sarcastic cheerful": "Sarcastic Cheerful", "reassuring": "Reassuring", "formal": "Formal", "anguished": "Anguished", "giggling": "Giggling", "exaggerated": "Exaggerated", "cold": "Cold", "hot-tempered": "Hot-tempered", "grateful": "Grateful", "regretful": "Regretful", "provocative": "Provocative", "triumphant": "Triumphant", "vengeful": "Vengeful", "heroic narration": "Heroic Narration", "villainous": "Villainous", "hypnotic": "Hypnotic", "desperate": "Desperate", "lamenting": "Lamenting", "celebratory": "Celebratory", "teasing": "Teasing", "exhausted": "Exhausted", "questioning suspicious": "Questioning Suspicious", "optimistic": "Optimistic", "bright, gentle voice, expressing excitement.": "Bright, gentle voice, expressing excitement.", "low, slow voice, conveying deep emotions.": "Low, slow voice, conveying deep emotions.", "sharp, exaggerated voice, expressing frustration.": "Sharp, exaggerated voice, expressing frustration.", "fast, lively voice, full of enthusiasm.": "Fast, lively voice, full of enthusiasm.", "interrupted, joyful voice, interspersed with laughter.": "Interrupted, joyful voice, interspersed with laughter.", "shaky, low voice, expressing pain.": "Shaky, low voice, expressing pain.", "gentle, steady voice, providing reassurance.": "Gentle, steady voice, providing reassurance.", "mature, clear voice, suitable for formal content.": "Mature, clear voice, suitable for formal content.", "weary, slightly irritated voice.": "Weary, slightly irritated voice.", "bright voice, conveying positivity and hope.": "Bright voice, conveying positivity and hope.", "natural, gentle voice with a slow rhythm.": "Natural, gentle voice with a slow rhythm.", "lively, engaging voice, captivating for children.": "Lively, engaging voice, captivating for children.", "even, slow voice, emphasizing content meaning.": "Even, slow voice, emphasizing content meaning.", "rhythmic, emotional voice, conveying subtlety.": "Rhythmic, emotional voice, conveying subtlety.", "low, slow voice, evoking curiosity.": "Low, slow voice, evoking curiosity.", "strong, passionate voice, driving action.": "Strong, passionate voice, driving action.", "high, interrupted voice, expressing astonishment.": "High, interrupted voice, expressing astonishment.", "firm, powerful voice, persuasive and assuring.": "Firm, powerful voice, persuasive and assuring.", "sweet, gentle voice, suitable for emotional content.": "Sweet, gentle voice, suitable for emotional content.", "shaky, interrupted voice, conveying anxiety.": "Shaky, interrupted voice, conveying anxiety.", "deep, strong voice with emphasis, creating suspense.": "Deep, strong voice with emphasis, creating suspense.", "engaging, lively voice, emphasizing product benefits.": "Engaging, lively voice, emphasizing product benefits.", "formal, clear voice with focus on key points.": "Formal, clear voice with focus on key points.", "calm, profound voice, delivering authenticity.": "Calm, profound voice, delivering authenticity.", "standard, neutral voice, clear and precise.": "Standard, neutral voice, clear and precise.", "bright, neutral voice, suitable for concise updates.": "Bright, neutral voice, suitable for concise updates.", "fast, lively voice, stimulating excitement.": "Fast, lively voice, stimulating excitement.", "friendly, approachable voice, encouraging engagement.": "Friendly, approachable voice, encouraging engagement.", "empathetic, gentle voice, easy to connect with.": "Empathetic, gentle voice, easy to connect with.", "clear voice, emphasizing questions and answers.": "Clear voice, emphasizing questions and answers.", "cheerful, playful voice with a hint of mischief.": "Cheerful, playful voice with a hint of mischief.", "slow, soft voice lacking energy.": "Slow, soft voice lacking energy.", "ironic, sharp voice, sometimes humorous.": "Ironic, sharp voice, sometimes humorous.", "cold voice, clearly expressing discomfort.": "Cold voice, clearly expressing discomfort.", "soft, mysterious voice, creating intimacy.": "Soft, mysterious voice, creating intimacy.", "emotional voice, convincing the listener to act.": "Emotional voice, convincing the listener to act.", "gentle voice, evoking feelings of reminiscence.": "Gentle voice, evoking feelings of reminiscence.", "even, relaxing voice, suitable for mindfulness.": "Even, relaxing voice, suitable for mindfulness.", "clear voice, emphasizing key words.": "Clear voice, emphasizing key words.", "confident, clear voice, ideal for business presentations.": "Confident, clear voice, ideal for business presentations.", "natural, friendly voice, as if talking to a friend.": "Natural, friendly voice, as if talking to a friend.", "fast, powerful voice, creating tension and excitement.": "Fast, powerful voice, creating tension and excitement.", "emphasized, suspenseful voice, creating intensity.": "Emphasized, suspenseful voice, creating intensity.", "professional, formal voice, suitable for business content.": "Professional, formal voice, suitable for business content.", "energetic, lively voice, introducing new technologies.": "Energetic, lively voice, introducing new technologies.", "vibrant, cheerful voice, appealing to younger audiences.": "Vibrant, cheerful voice, appealing to younger audiences.", "gentle, empathetic voice, easing concerns.": "Gentle, empathetic voice, easing concerns.", "strong, decisive voice, full of inspiration.": "Strong, decisive voice, full of inspiration.", "bright, excited voice, suitable for celebrations.": "Bright, excited voice, suitable for celebrations.", "fast, strong voice, emphasizing urgency.": "Fast, strong voice, emphasizing urgency.", "passionate, inspiring voice, encouraging action.": "Passionate, inspiring voice, encouraging action.", "warm, approachable voice, fostering connection.": "Warm, approachable voice, fostering connection.", "fast, powerful voice, brimming with enthusiasm.": "Fast, powerful voice, brimming with enthusiasm.", "slow, gentle voice, evoking peace and tranquility.": "Slow, gentle voice, evoking peace and tranquility.", "firm, assertive voice, exuding confidence.": "Firm, assertive voice, exuding confidence.", "warm, captivating voice, leaving a strong impression.": "Warm, captivating voice, leaving a strong impression.", "flat, unvaried voice, conveying neutrality or irony.": "Flat, unvaried voice, conveying neutrality or irony.", "curious voice, emphasizing questions.": "Curious voice, emphasizing questions.", "firm, clear voice, guiding the listener step-by-step.": "Firm, clear voice, guiding the listener step-by-step.", "gentle, slow voice, evoking a floating sensation.": "Gentle, slow voice, evoking a floating sensation.", "deep, resonant voice, emphasizing grandeur.": "Deep, resonant voice, emphasizing grandeur.", "soft, melodic voice, similar to singing.": "Soft, melodic voice, similar to singing.", "low, drawn-out voice, evoking mystery.": "Low, drawn-out voice, evoking mystery.", "slow, low voice, conveying deep sadness.": "Slow, low voice, conveying deep sadness.", "bright, energetic voice, full of positivity.": "Bright, energetic voice, full of positivity.", "low, whispery voice, evoking fear or strangeness.": "Low, whispery voice, evoking fear or strangeness.", "sweet, teasing voice, full of allure.": "Sweet, teasing voice, full of allure.", "slow, reflective voice, full of contemplation.": "Slow, reflective voice, full of contemplation.", "resonant, emphasized voice, creating a movie-like effect.": "Resonant, emphasized voice, creating a movie-like effect.", "lighthearted, cheerful voice, sometimes exaggerated.": "Lighthearted, cheerful voice, sometimes exaggerated.", "clear, slow voice, guiding the listener step-by-step.": "Clear, slow voice, guiding the listener step-by-step.", "natural voice, as if chatting with the listener.": "Natural voice, as if chatting with the listener.", "soft, sincere voice, expressing regret.": "Soft, sincere voice, expressing regret.", "hesitant, uncertain voice, sometimes awkward.": "Hesitant, uncertain voice, sometimes awkward.", "warm voice, providing motivation and support.": "Warm voice, providing motivation and support.", "even voice, free of emotional bias.": "Even voice, free of emotional bias.", "strong, powerful voice, exuding credibility.": "Strong, powerful voice, exuding credibility.", "cheerful voice with an undertone of mockery.": "Cheerful voice with an undertone of mockery.", "gentle, empathetic voice, providing comfort.": "Gentle, empathetic voice, providing comfort.", "clear, polite voice, suited for formal occasions.": "Clear, polite voice, suited for formal occasions.", "urgent, shaky voice, expressing distress.": "<PERSON><PERSON>, shaky voice, expressing distress.", "interrupted voice, mixed with light laughter.": "Interrupted voice, mixed with light laughter.", "loud, emphasized voice, often humorous.": "Loud, emphasized voice, often humorous.", "flat, unemotional voice, conveying detachment.": "Flat, unemotional voice, conveying detachment.", "fast, sharp voice, sometimes out of control.": "Fast, sharp voice, sometimes out of control.", "warm, sincere voice, expressing appreciation.": "Warm, sincere voice, expressing appreciation.", "low, subdued voice, full of remorse.": "Low, subdued voice, full of remorse.", "challenging, strong voice, full of insinuation.": "Challenging, strong voice, full of insinuation.", "loud, powerful voice, full of victory.": "Loud, powerful voice, full of victory.", "low, cold voice, expressing determination for revenge.": "Low, cold voice, expressing determination for revenge.", "strong, inspiring voice, emphasizing heroic deeds.": "Strong, inspiring voice, emphasizing heroic deeds.", "low, drawn-out voice, full of scheming.": "Low, drawn-out voice, full of scheming.", "even, repetitive voice, drawing the listener in.": "Even, repetitive voice, drawing the listener in.", "urgent, shaky voice, expressing hopelessness.": "<PERSON><PERSON>, shaky voice, expressing hopelessness.", "low, sorrowful voice, as if mourning.": "Low, sorrowful voice, as if mourning.", "excited, joyful voice, full of festive spirit.": "Excited, joyful voice, full of festive spirit.", "light, playful voice, sometimes mockingly.": "Light, playful voice, sometimes mockingly.", "weak, broken voice, expressing extreme fatigue.": "Weak, broken voice, expressing extreme fatigue.", "slow, emphasized voice, full of suspicion.": "Slow, emphasized voice, full of suspicion.", "bright, hopeful voice, creating positivity.": "Bright, hopeful voice, creating positivity.", "Your audio will be processed with the latest stable model.": "Your audio will be processed with the latest stable model.", "Your audio will be processed with the latest beta model.": "Your audio will be processed with the latest beta model.", "You don't have any saved prompts yet.": "You don't have any saved prompts yet.", "Commercial Use": "Commercial Use", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes.": "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes.", "This field is required.": "This field is required.", "Must be at least 8 characters": "Must be at least 8 characters", "Passwords must be different": "Passwords must be different", "Passwords must match": "Passwords must match", "Current password": "Current password", "New password": "New password", "Confirm new password": "Confirm new password", "Update": "Update", "Password": "Password", "Confirm your current password before setting a new one.": "Confirm your current password before setting a new one.", "Account": "Account", "No longer want to use our service? You can delete your account here. This action is not reversible. All information related to this account will be deleted permanently.": "No longer want to use our service? You can delete your account here. This action is not reversible. All information related to this account will be deleted permanently.", "Delete account": "Delete account", "Your saved prompts": "Your saved prompts", "Custom prompt {count}": "Custom prompt {count}", "Success": "Success", "Saved prompt successfully": "Saved prompt successfully", "Error": "Error", "Saved prompt failed": "Saved prompt failed", "Updated prompt successfully": "Updated prompt successfully", "Updated prompt failed": "Updated prompt failed", "Prompt": "Prompt", "Are you sure you want to delete this prompt?": "Are you sure you want to delete this prompt?", "Deleted prompt successfully": "Deleted prompt successfully", "Deleted prompt failed": "Deleted prompt failed", "Custom prompt": "Custom prompt", "Enter your custom prompt here.": "Enter your custom prompt here.", "Prompt name": "Prompt name", "This name will help you identify your prompt.": "This name will help you identify your prompt.", "Ex: Funny prompt": "Ex: Funny prompt", "Discard": "Discard", "Ok, save it!": "Ok, save it!", "Save as new": "Save as new", "Don't use": "Don't use", "Use": "Use", "Edit": "Edit", "Other people’s privacy": "Other people’s privacy", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.", "{price}$ per credit": "{price}$ per credit", "Pricing": "Pricing", "Simple and flexible. Only pay for what you use.": "Simple and flexible. Only pay for what you use.", "Pay as you go": "Pay as you go", "Flexible": "Flexible", "Input characters": "Input characters", "Audio model": "Audio model", "Credits": "Credits", "Cost": "Cost", "HD quality voices": "HD quality voices", "Advanced model": "Advanced model", "Buy now": "Buy now", "Paste your text to calculate": "Paste your text to calculate", "Paste your text here...": "Paste your text here...", "Video Gen": "Video Gen", "Generate videos from text prompts and images.": "Generate videos from text prompts and images.", "Speech Gen": "Speech Gen", "Convert text and documents to natural speech.": "Convert text and documents to natural speech.", "Gemini 2.5 Flash": "Gemini 2.5 Flash", "Gemini 2.5 Pro": "Gemini 2.5 Pro", "1M characters": "1M characters", "Contact": "Contact", "Voice": "Voice", "Emotion": "Emotion", "Language": "Language", "Custom Prompt": "Custom Prompt", "Output Format": "Output Format", "Speed": "Speed", "Document": "Document", "Support {n}+ voices": "Support {n}+ voices", "Support {n}+ emotions": "Support {n}+ emotions", "Support {n}+ languages": "Support {n}+ languages", "Support custom prompt": "Support custom prompt", "Support MP3 and WAV": "Support MP3 and WAV", "Support speed control": "Support speed control", "Support document to speech": "Support document to speech", "Dialogue Gen": "Dialogue Gen", "Create natural conversations with multiple speakers.": "Create natural conversations with multiple speakers.", "Veo 2": "Veo 2", "Text to Video": "Text to Video", "Image to Video": "Image to Video", "Up to 8 seconds": "Up to 8 seconds", "1080p Quality": "1080p Quality", "Multiple Styles": "Multiple Styles", "Text to Speech": "Text to Speech", "Document to Speech": "Document to Speech", "Multi-Speaker Support": "Multi-Speaker Support", "50+ Voices": "50+ Voices", "Multiple Languages": "Multiple Languages", "Emotion Control": "Emotion Control", "Multi-Speaker Dialogue": "Multi-Speaker Dialogue", "Natural Conversations": "Natural Conversations", "Voice Customization": "Voice Customization", "Emotion Expression": "Emotion Expression", "Script Generation": "Script Generation", "Audio Export": "Audio Export", "Calculate": "Calculate", "Estimate your cost by drag the slider below or": "Estimate your cost by drag the slider below or", "calming": "Calming", "customer": "Customer", "exciting": "exciting", "excuse": "Excuse", "game": "Game", "hot": "Hot", "kids": "Kids", "professional": "Professional", "tech": "Tech", "trailer": "Trailer", "weather": "Weather", "No thumbnail available": "No thumbnail available", "Debit or Credit Card": "Debit or Credit Card", "Visa, Mastercard, American Express and more": "Visa, Mastercard, American Express and more", "Top up now": "Top up now", "noVideoAvailable": "No video available", "assignVoicesToSpeakers": "Assign Voices to Speakers", "speakers": "Speakers", "addSpeaker": "Add Speaker", "noVoiceAssigned": "No voice assigned", "noSpeakersAdded": "No speakers added yet", "assignVoiceToSpeaker": "Assign voice to {speaker}", "assigned": "Assigned", "assign": "Assign", "editSpeaker": "Edit Speaker", "speakerName": "Speaker Name", "enterSpeakerName": "Enter speaker name", "save": "Save", "speaker": "Speaker", "assignVoices": "Assign Voices", "speakersWithVoices": "{assigned}/{total} speakers have voices", "dialogs": "Dialogs", "addDialog": "Add Dialog", "enterDialogText": "Enter dialog text...", "selectSpeaker": "Select Speaker", "generateDialogSpeech": "Generate Dialog Speech", "voice 1": "Voice 1", "voice 2": "Voice 2", "uuid": "UUID", "output_format": "Output Format", "output_channel": "Output Channel", "file_name": "File Name", "file_size": "File Size", "speakers_count": "Speakers Count", "custom_prompt": "Custom Prompt", "Please wait a moment...": "Please wait a moment...", "Click to copy": "Click to copy", "Copied to clipboard": "Copied to clipboard", "UUID has been copied to clipboard": "UUID has been copied to clipboard", "Credits: {credits} remaining": "Credits: {credits} remaining", "This generation will cost: {cost} Credits": "This generation will cost: {cost} Credits", "This generation will cost: {cost} Credits for {duration}s": "This generation will cost: {cost} Credits for {duration}s", "Your generated video will appear here": "Your generated video will appear here", "Regenerate Video": "Regenerate Video", "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?": "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?", "Your generated speech will appear here": "Your generated speech will appear here", "Regenerate Speech": "Regenerate Speech", "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?": "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?", "Generated Speech": "Generated Speech", "Generating speech...": "Generating speech...", "View Details": "View Details", "Speech Examples": "Speech Examples", "Click on any example to use its prompt for speech generation": "Click on any example to use its prompt for speech generation", "Click to use": "Click to use", "Style Description": "Style Description", "Dialog Content": "Dialog Content", "Your generated dialog will appear here": "Your generated dialog will appear here", "Regenerate Dialog": "Regenerate Dialog", "Generated Dialog": "Generated Dialog", "Generating dialog...": "Generating dialog...", "Dialog Information": "Dialog Information", "Audio Player": "Audio Player", "Voices": "Voices", "Voice 1": "Voice 1", "Voice 2": "Voice 2", "Dialog Examples": "Dialog Examples", "Click on any example to use its style or dialog content": "Click on any example to use its style or dialog content", "Use Style": "Use Style", "Use Dialog": "Use Dialog", "videoStyles.selectVideoStyle": "Select Video Style", "videoStyles.cinematic": "Cinematic", "videoStyles.realistic": "Realistic", "videoStyles.animated": "Animated", "videoStyles.artistic": "Artistic", "videoStyles.documentary": "Documentary", "videoStyles.vintage": "Vintage", "ui.buttons.downloadApp": "Download App", "ui.buttons.signUp": "Sign Up", "ui.buttons.viewDetails": "View Details", "ui.buttons.seeLater": "See Later", "ui.buttons.selectFile": "Select File", "ui.buttons.selectFiles": "Select Files", "ui.buttons.pickAVoice": "Pick a voice", "ui.buttons.topUpNow": "Top up now", "ui.buttons.pressEscToClose": "Press ESC to close", "ui.labels.clickToCopy": "Click to copy", "ui.labels.copiedToClipboard": "Copied to clipboard", "ui.labels.noAudioAvailable": "No audio available", "ui.labels.noThumbnailAvailable": "No thumbnail available", "ui.labels.noPromptAvailable": "No prompt available", "ui.labels.videoModel": "Video Model", "ui.labels.speechModel": "Speech Model", "ui.labels.generatedSpeech": "Generated Speech", "ui.labels.generatedAudio": "Generated Audio", "ui.labels.defaultVoice": "Default Voice", "ui.labels.selectAnyVoice": "Select any voice", "ui.labels.cameraMotion": "Camera motion", "ui.labels.transform": "Transform", "ui.labels.transforming": "Transforming...", "ui.actions.showResult": "Show Result", "ui.actions.hideResult": "<PERSON><PERSON> Result", "ui.messages.imageLoaded": "Image loaded", "ui.messages.imageRevealComplete": "Image reveal complete", "ui.messages.processingImage": "Processing image", "ui.messages.videoLoaded": "Video loaded", "ui.messages.videoProcessing": "Video processing", "ui.messages.speechGenerating": "Generating speech...", "ui.messages.invalidDownloadLink": "Invalid download link", "ui.messages.pleaseSelectSupportedFile": "Please select a supported file", "ui.messages.dragDropOrClick": "Drag & drop files here or click to select", "ui.messages.dropFilesHere": "Drop files here", "ui.messages.selectMultipleFiles": "You can select multiple files", "ui.messages.selectSingleFile": "Select a file to upload", "ui.messages.supportedFormats": "Supported formats", "ui.messages.releaseToUpload": "Release to upload", "ui.messages.deleteConfirm": "Confirm Delete", "ui.messages.deleteFailed": "Delete failed", "Delete Voice": "Delete Voice", "Are you sure you want to delete this voice? This action cannot be undone.": "Are you sure you want to delete this voice? This action cannot be undone.", "Voice deleted successfully": "Voice deleted successfully", "Failed to delete voice": "Failed to delete voice", "ui.messages.youHaveNewNotification": "You have a new notification", "ui.messages.yourGenerationIsReady": "Your generation is ready", "ui.errors.errorLoadingImage": "Error loading image:", "ui.errors.failedToCopy": "Failed to copy: ", "ui.errors.failedToPlayAudioPreview": "Failed to play audio preview:", "ui.errors.wavesurferError": "<PERSON><PERSON><PERSON><PERSON> error:", "ui.errors.somethingWentWrong": "Something went wrong. Please try again. ", "ui.errors.supabaseUrlRequired": "Supabase URL and anonymous key are required", "dialog.startTypingHere": "Start typing dialog here...", "payment.debitCreditCard": "Debit or Credit Card", "payment.cardDescription": "Visa, Mastercard, American Express and more", "personGeneration": "Person Generation", "Imagen": "Imagen", "On": "On", "Off": "Off", "Prompts will always be refined to improve output quality": "Prompts will always be refined to improve output quality", "Prompts will not be modified": "Prompts will not be modified", "Tips": "Tips", "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.": "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.", "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.": "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.", "Go to History": "Go to History", "footer.youtube": "Youtube", "footer.doctransGPT": "DoctransGPT", "footer.textToSpeechOpenAI": "Text To Speech OpenAI", "footer.privacyPolicy": "Privacy Policy", "footer.termsOfService": "Terms of Service", "footer.terms": "Terms", "footer.privacy": "Privacy", "Generate": "Generate", "Generate Video": "Generate Video", "ui.errors.generationFailed": "Generation failed", "ui.errors.unknownError": "An unknown error occurred", "ui.errors.tryAgainLater": "Please try again later", "ui.errors.viewGooglePolicy": "View Google's Generative AI Use Policy", "downloadVideo": "Download Video", "imageStyles.selectImageStyle": "Select Image Style", "imageStyles.none.description": "No specific style applied", "imageStyles.3d-render.description": "Render image in 3D", "imageStyles.acrylic.description": "Create image with acrylic paint style", "imageStyles.anime-general.description": "Generate image in anime style", "imageStyles.creative.description": "Apply creative artistic effects", "imageStyles.dynamic.description": "Create dynamic and energetic visuals", "imageStyles.fashion.description": "Style image for fashion photography", "imageStyles.game-concept.description": "Design image for game concept art", "imageStyles.graphic-design-3d.description": "Apply 3D graphic design elements", "imageStyles.illustration.description": "Create illustration-style artwork", "imageStyles.portrait.description": "Optimize for portrait photography", "imageStyles.portrait-cinematic.description": "Create cinematic portrait style", "imageStyles.portrait-fashion.description": "Apply fashion portrait styling", "imageStyles.ray-traced.description": "Render with ray tracing effects", "imageStyles.stock-photo.description": "Create professional stock photo style", "imageStyles.watercolor.description": "Apply watercolor painting effects", "imageStyles.examples": "Examples", "downloadAudio": "Download Audio", "All Countries": "All Countries", "All Genders": "All Genders", "Country": "Country", "Gender": "Gender", "Reset": "Reset", "Search by name or description...": "Search by name or description...", "Male": "Male", "Female": "Female", "American": "American", "British": "British", "Australian": "Australian", "Indian": "Indian", "Chinese": "Chinese", "Spanish": "Spanish", "Canadian": "Canadian", "Irish": "Irish", "Singaporean": "Singaporean", "Russian": "Russian", "German": "German", "Portuguese": "Portuguese", "Hindi": "Hindi", "Mexican": "Mexican", "Latin American": "Latin American", "Argentine": "Argentine", "Peninsular": "Peninsular", "French": "French", "Parisian": "Parisian", "Standard": "Standard", "Brazilian": "Brazilian", "Turkish": "Turkish", "Istanbul": "Istanbul", "Bavarian": "Bavarian", "Polish": "Polish", "Italian": "Italian", "South African": "South African", "Scottish": "Scottish", "Welsh": "Welsh", "New Zealand": "New Zealand", "Dutch": "Dutch", "Belgian": "Belgian", "Swedish": "Swedish", "Norwegian": "Norwegian", "Danish": "Danish", "Korean": "Korean", "Korean, Seoul": "Korean, Seoul", "Japanese": "Japanese", "Croatian": "Croatian", "Czech": "Czech", "Moravian": "Moravian", "Zealandic": "Zealandic", "Indonesian": "Indonesian", "Javanese": "Javanese", "Romanian": "Romanian", "Swiss": "Swiss", "Vietnamese": "Vietnamese", "Arabic": "Arabic", "Bulgarian": "Bulgarian", "Finnish": "Finnish", "Greek": "Greek", "Hungarian": "Hungarian", "Filipino": "Filipino", "History": "History", "imagen-flash": "Gemini 2.0 Flash", "Detail": "Detail", "Delete": "Delete", "More": "More", "tts-text": "Audio", "tts-document": "Audio", "tts-multi-speaker": "Audio", "tts-history": "Audio", "tts-history_1": "Audio", "tts-history_2": "Audio", "tts-history_3": "Audio", "voice-training": "Voice Training", "voice-training_1": "Voice Training", "voice-training_2": "Voice Training", "Start writing or paste your text here or select a file to generate speech...": "Start writing or paste your text here or select a file to generate speech...", "Selecting a voice...": "Selecting a voice...", "Voices Library": "Voices Library", "Select a voice for your speaker from the library.": "Select a voice for your speaker from the library.", "Next": "Next", "Back": "Back", "Done": "Done", "I got it!": "I got it!", "Press ESC to close": "Press ESC to close", "Your generated image will appear here": "Your generated image will appear here", "Generate Speech": "Generate Speech", "Start writing or paste your text here to generate speech...": "Start writing or paste your text here to generate speech...", "Home": "Home", "Price per 1 character: {cost} Credits": "Price per 1 character: {cost} Credits", "veo-2": "Veo 2", "veo-3": "Veo 3", "Your speech generation is still being generated in the background. You can close this page and check the history tab for the generated speech and we will notify you when it is ready.": "Your speech generation is still being generated in the background. You can close this page and check the history tab for the generated speech and we will notify you when it is ready.", "Create Another": "Create Another", "estimated_credit": "Estimated Credit", "tts-flash": "Gemini 2.5 Flash", "Select Another Voice": "Select Another Voice", "Your credits will never expire.": "Your credits will never expire.", "Available credits": "Available credits", "{n}+ Styles": "{n}+ Styles", "Create images from text prompts.": "Create images from text prompts.", "/Image": "/Image", "/Video": "/Video", "/1 character": "/1 character", "Buy credits": "Buy credits", "My Account": "My Account", "Manage your account, credits, and orders.": "Manage your account, credits, and orders.", "Full Name": "Full Name", "Total Available Credits": "Total Available Credits", "Locked Credits": "Locked Credits", "Save changes": "Save changes", "Your account has been updated.": "Your account has been updated.", "User Info": "User Info", "Email": "Email", "Used to sign in, for email receipts and product updates.": "Used to sign in, for email receipts and product updates.", "Active and valid credits only": "Active and valid credits only", "We lock your credits to perform transactions.": "We lock your credits to perform transactions.", "Referral Link": "Referral Link", "Share your referral link to earn credits.": "Share your referral link to earn credits.", "Referral Code": "Referral Code", "Your Referral Code": "Your Referral Code", "Copy": "Copy", "Copied!": "Copied!", "Orders": "Orders", "Manage your orders.": "Manage your orders.", "Will appear on receipts, invoices, and other communication.": "Will appear on receipts, invoices, and other communication.", "User Information": "User Information", "Change Password": "Change Password", "Security": "Security", "Credit Statistics": "Credit Statistics", "enhance_prompt": "Enhance Prompt", "Current Plan": "Current Plan", "When you buy credits, you will be upgraded to Premium Plan.": "When you buy credits, you will be upgraded to Premium Plan.", "Available Credits": "Available Credits", "Purchased Credits": "Purchased Credits", "Plan Credits": "Plan Credits", "profile.passwordChanged": "Password Changed", "profile.passwordChangedDescription": "Your password has been changed successfully", "profile.passwordChangeError": "Password Change Failed", "profile.passwordChangeErrorDescription": "There was an error changing your password. Please try again.", "delete": "Delete", "profile.deleteAccount": "Delete Account", "profile.deleteAccountConfirmation": "Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.", "profile.accountDeleted": "Account Deleted", "profile.accountDeletedDescription": "Your account has been deleted successfully", "profile.accountDeletionError": "Account Deletion Failed", "profile.accountDeletionErrorDescription": "There was an error deleting your account. Please try again.", "To celebrate our launch, enjoy 50% off select Gemini API models. Offer valid until further notice.": "To celebrate our launch, enjoy 50% off select Gemini API models. Offer valid until further notice.", "Check now": "Check now", "payment.success.title": "Payment Successful!", "payment.success.message": "Thank you for your purchase! Your payment has been processed successfully.", "payment.success.orderId": "Order ID:", "payment.success.redirecting": "Redirecting to your orders in {seconds} seconds...", "payment.success.viewOrders": "View My Orders", "payment.error.title": "Payment Error", "payment.error.message": "There was an issue processing your payment. Please contact support if this continues.", "payment.error.backToOrders": "Back to Orders", "Overview of your credits status.": "Overview of your credits status.", "Payment History": "Payment History", "Your payment history will appear here once you have made a purchase.": "Your payment history will appear here once you have made a purchase.", "Payment method": "Payment method", "Purchase Date": "Purchase Date", "Amount": "Amount", "Status": "Status", "Payment amount": "Payment amount", "payment.status.unavailable": "Unavailable", "payment.status.created": "Created", "payment.status.completed": "Completed", "payment.status.failed": "Failed", "payment.status.canceled": "Canceled", "payment.status.processing": "Processing", "payment.status.refund": "Refund", "payment.status.partial_paid": "Partial Paid", "Integration": "Integration", "API Keys": "API Keys", "Manage your API keys.": "Manage your API keys.", "Create API Key": "Create API Key", "Name your API key.": "Name your API key.", "Create": "Create", "You have not created any API keys yet.": "You have not created any API keys yet.", "Copy API Key": "Copy API Key", "Delete API Key": "Delete API Key", "EMAIL_NOT_EXIST": "Email does not exist", "Your account is not verified": "Your account is not verified", "Your account is not verified. Please verify your account to continue": "Your account is not verified. Please verify your account to continue", "TOKEN_USED": "Token already used", "NOT_ENOUGH_CREDIT": "Not enough credit. Please top up your account.", "Not enough credit": "Not enough credit", "Your account does not have enough credit. Please top up your account to continue.": "Your account does not have enough credit. Please top up your account to continue.", "{n} credits": "{n} credits", "USD / {unit}": "USD / {unit}", "Credits / {unit}": "Credits / {unit}", "Save {n}%": "Save {n}%", "${price} = {n} credits": "${price} = {n} credits", "You can switch between money and credits to see the price in your preferred currency.": "You can switch between money and credits to see the price in your preferred currency.", "Forever": "Forever", "For large organizations.": "For large organizations.", "Free": "Free", "Contact us": "Contact us", "Contact sales": "Contact sales", "Premium": "Premium", "Enterprise": "Enterprise", "Show money": "Show money", "Show credits": "Show credits", "Auto upgrade after buy credits": "Auto upgrade after buy credits", "Image": "Image", "Video": "Video", "Audio": "Audio", "Dialog": "Dialog", "Get started": "Get started", "Image Style": "Image Style", "Image Aspect Ratio": "Image Aspect Ratio", "Enhance Prompt": "Enhance Prompt", "Aspect Ratio": "Aspect Ratio", "Support multiple aspect ratio": "Support multiple aspect ratio", "Support enhance prompt": "Support enhance prompt", "Up to {size}MB": "Up to {size}MB", "SIGNUP_MAIL_EXIST": "Email already exists", "SIGNIN_USER_NOT_FOUND": "User not found", "SIGNIN_USER_NOT_VERIFIED": "User not verified", "SIGNIN_USER_DISABLED": "User disabled", "SIGNIN_WRONG_PASSWORD": "Wrong password", "SIGNIN_USER_NOT_FOUND_FOR_EMAIL": "User not found for email", "SIGNIN_INVALID_EMAIL": "Invalid email", "auth.accountCreated": "Account created", "auth.accountCreatedDescription": "Your account has been created successfully. Please check your email to verify your account.", "Select voice on right": "Select voice on right", "second": "second", "Current plan": "Current plan", "Already premium": "Already premium", "Enjoy 50% off select Gemini API models. Offer valid until further notice.": "Enjoy 50% off select Gemini API models. Offer valid until further notice.", "Text must be at least 4 characters long.": "Text must be at least 4 characters long.", "Each dialog text must be at least 4 characters long.": "Each dialog text must be at least 4 characters long.", "Please enter text or select a file to generate speech.": "Please enter text or select a file to generate speech.", "Please select a voice for speech generation.": "Please select a voice for speech generation.", "Please add at least one dialog to generate speech.": "Please add at least one dialog to generate speech.", "Please select voices for both speakers.": "Please select voices for both speakers.", "Photorealistic": "Photorealistic", "imageStyles.photorealistic.description": "Realistic image with high detail and resolution", "Create your first voice": "Create your first voice", "Create Custom Voice": "Create Custom Voice", "Type of voice to create": "Type of voice to create", "Instant Voice Cloning": "Instant Voice Cloning", "Clone a voice from a clean sample recording. Samples should contain 1 speaker and be over 1 minute long and not contain background noise": "<PERSON>lone a voice from a clean sample recording. Samples should contain 1 speaker and be over 1 minute long and not contain background noise", "Professional Voice Cloning": "Professional Voice Cloning", "Create the most realistic digital replica of your voice.": "Create the most realistic digital replica of your voice.", "Speaker Name": "Speaker Name", "Enter speaker name": "Enter speaker name", "Describe the voice characteristics": "Describe the voice characteristics", "Select gender": "Select gender", "Select age": "Select age", "Select accent": "Select accent", "Audio Sample": "Audio Sample", "I agree to the privacy policy": "I agree to the privacy policy", "Note:": "Note:", "The sound should be clear, without any noise, and last around 1 minutes to ensure good quality.": "The sound should be clear, without any noise, and last around 1 minutes to ensure good quality.", "The sound should be clear, without any noise, and last around 10 minutes to ensure good quality.": "The sound should be clear, without any noise, and last around 10 minutes to ensure good quality.", "It will cost 0 credits each time you create a voice.": "It will cost 0 credits each time you create a voice.", "It will cost 5,000 credits each time you create a voice.": "It will cost 5,000 credits each time you create a voice.", "Maximum file size": "Maximum file size", "File size exceeds maximum limit of {maxSize}": "File size exceeds maximum limit of {maxSize}", "File size exceeds 150MB limit": "File size exceeds 150MB limit", "Young": "<PERSON>", "Middle": "Middle", "Old": "Old", "English": "English", "Custom voice created successfully": "Custom voice created successfully", "Voice Training Started": "Voice Training Started", "Your custom voice is being trained. You will be notified when it's ready.": "Your custom voice is being trained. You will be notified when it's ready.", "Voice Training Complete": "Voice Training Complete", "Your custom voice is ready to use!": "Your custom voice is ready to use!", "Voice Training Failed": "Voice Training Failed", "Voice training failed. Please try again.": "Voice training failed. Please try again.", "Training": "Training", "Failed": "Failed", "Voice Training in Progress": "Voice Training in Progress", "Your custom voice is being trained. This process may take up to 30 minutes. You will be notified when it's ready.": "Your custom voice is being trained. This process may take up to 30 minutes. You will be notified when it's ready.", "Voice Name": "Voice Name", "Training Type": "Training Type", "Training Progress": "Training Progress", "Estimated time: 5-30 minutes": "Estimated time: 5-30 minutes", "Refresh Status": "Refresh Status", "Voice Not Available": "Voice Not Available", "This voice is currently being trained and cannot be selected yet.": "This voice is currently being trained and cannot be selected yet.", "Failed to create custom voice": "Failed to create custom voice", "validation.mustAgreeToPrivacy": "You must agree to the privacy policy", "I agree to the {0}": "I agree to the {0}", "Privacy Policy": "Privacy Policy", "negativePrompt": "Negative Prompt", "negativePromptDescription": "Describe what you don't want to see in the video", "negativePromptTooltip": "Negative prompts help exclude unwanted elements, styles, or concepts from your video generation. For example: 'blurry, low quality, distorted faces'", "negativePromptPlaceholder": "Enter what you want to avoid in the video...", "negativePromptSuggestions": "Quick suggestions", "negativePromptSuggestion1": "blurry, low quality", "negativePromptSuggestion2": "distorted faces, deformed", "negativePromptSuggestion3": "text, watermark", "negativePromptSuggestion4": "dark, underexposed", "negativePromptSuggestion5": "shaky, unstable motion", "negativePromptSuggestion6": "pixelated, artifacts", "negative_prompt": "Negative Prompt", "aspect_ratio": "Aspect Ratio", "person_generation": "Person Generation", "ALLOW_ADULT": "Allow Adult", "ALLOW_ALL": "Allow All", "veo-3-fast": "Veo 3 Fast", "Click to copy. UUID is unique and can be used to contact support.": "Click to copy. UUID is unique and can be used to contact support.", "hero.title": "Create Amazing AI Content", "hero.subtitle": "Transform your ideas into stunning AI-generated images, videos, speech, and more. Save up to 60% compared to traditional creative services while experiencing the future of content generation.", "hero.cta.primary": "Start Creating", "hero.cta.secondary": "Watch Demo", "hero.users": "Trusted by 10,000+ creators worldwide", "hero.scroll": "Scroll to explore", "features.title": "Key Features", "features.subtitle": "Discover powerful features that help you create professional AI content", "features.ai.title": "Advanced AI", "features.ai.description": "Use cutting-edge AI technology to generate high-quality content with amazing accuracy.", "features.speed.title": "Fast Generation", "features.speed.description": "Transform your ideas into content in just seconds. No long waiting times.", "features.creative.title": "Unlimited Creativity", "features.creative.description": "Create content in any style, from animation to realistic, from artistic to professional.", "features.quality.title": "High Quality", "features.quality.description": "Output with high resolution, smooth motion, and sharp details.", "features.collaboration.title": "Easy Collaboration", "features.collaboration.description": "Share and collaborate on projects with your team easily.", "features.export.title": "Multi-Format Export", "features.export.description": "Export content in various formats suitable for all platforms and purposes.", "features.cta.title": "Ready to get started?", "features.cta.description": "Experience the power of AI in content creation today", "features.cta.button": "Create Your First Content", "howto.title": "How to Create AI Content", "howto.subtitle": "With just 4 simple steps, you can create professional AI content", "howto.step1.title": "Describe Your Idea", "howto.step1.description": "Write a detailed description of the content you want to create. Include scenes, characters, actions, and style.", "howto.step2.title": "Customize Settings", "howto.step2.description": "Choose resolution, aspect ratio, style, and other parameters suitable for your purpose.", "howto.step3.title": "AI Processing", "howto.step3.description": "Artificial intelligence will analyze your request and create high-quality content in seconds.", "howto.step4.title": "Download & Share", "howto.step4.description": "Download content to your computer or share directly to your favorite social media platforms.", "howto.examples.title": "AI Video Generation Examples", "howto.examples.subtitle": "Explore the power of AI video generation with these interactive examples. Hover to preview, click to try the prompt.", "howto.examples.tryPrompt": "Try This Prompt", "howto.examples.example1.title": "Cherry Blossom Cat", "howto.examples.example1.prompt": "A cat running through a cherry blossom field at sunset", "howto.examples.example2.title": "Ocean Surfing", "howto.examples.example2.prompt": "A man surfing on blue ocean waves on a beautiful sunny day", "howto.examples.example3.title": "Future City", "howto.examples.example3.prompt": "A futuristic city with flying cars and towering skyscrapers", "howto.examples.example4.title": "Cultural Dance", "howto.examples.example4.prompt": "Traditional dancers performing in colorful costumes at a festival", "howto.examples.example5.title": "Mountain Landscape", "howto.examples.example5.prompt": "A serene mountain landscape with flowing rivers and misty peaks", "howto.cta": "Try Creating Now", "faq.title": "Frequently Asked Questions", "faq.description": "Find answers to common questions about AI content creation", "faq.general.title": "General", "faq.services.title": "Services", "faq.pricing.title": "Pricing", "faq.questions.q1.title": "Why choose geminigen.ai over other TTV tools?", "faq.questions.q1.content": "Geminigen.ai offers AI-generated images and videos from text at a more affordable price compared to other applications on the market. In addition to image and video generation from text, we also provide text-to-speech services and text-based conversation generation.", "faq.questions.q2.title": "How do I use the geminigen.ai service?", "faq.questions.q2.content": "Our service is designed to be user-friendly. Simply describe the video you want in text, and the system will automatically convert it into a video.", "faq.questions.q3.title": "Do I need programming knowledge to use geminigen.ai?", "faq.questions.q3.content": "No, you don't need any programming knowledge. We've integrated Gemini's TTV API into our website, making the process of converting text into images and videos simple and convenient for everyone.", "faq.questions.q4.title": "What languages are supported for text input?", "faq.questions.q4.content": "We currently support multiple languages including English, Vietnamese, Spanish, and more. Support for additional languages is being added regularly.", "faq.questions.q5.title": "What is the cost of using geminigen.ai?", "faq.questions.q5.content": "We price based on Gemini's pricing, ensuring costs are lower than many other text-to-speech conversion tools on the market. You can refer to detailed pricing on the page: https://geminigen.ai/pricing", "faq.questions.q6.title": "Can I use geminigen.ai for commercial purposes?", "faq.questions.q6.content": "Yes, our service supports both personal and commercial purposes. However, please ensure compliance with our terms of use.", "faq.questions.q7.title": "What is the quality of the video on geminigen.ai?", "faq.questions.q7.content": "The generated images and videos are high-quality, with realistic and vivid visuals thanks to Gemini's advanced TTV technology.", "faq.questions.q8.title": "How do I protect my privacy and data on geminigen.ai?", "faq.questions.q8.content": "User security and privacy are our top priorities. We employ advanced security measures to protect your data and do not share information with any third parties without your consent.", "faq.questions.q9.title": "For Text to Speech conversion, Can I edit or customize the output audio?", "faq.questions.q9.content": "While we do not provide direct editing functions on the platform, you can customize some settings such as reading speed and tone before conversion. This allows you to have better control over the feel and final sound of the output file.", "faq.questions.q10.title": "For Text to Speech conversion, Can I request additional voices or new languages?", "faq.questions.q10.content": "We always listen to user feedback and strive to expand our services. If you have specific requests for a particular voice or language, please feel free to submit them to our support system.", "faq.questions.q11.title": "Where can I contact technical support if I encounter issues using the service?", "faq.questions.q11.content": "We provide support via email and live chat on the website. Our support team is always ready to answer any questions and assist you whenever needed.", "faq.questions.q12.title": "Do I need to create an account to use the service?", "faq.questions.q12.content": "Yes, creating an account helps you manage converted imagen, video, documents easily and access advanced features and better customer support services.", "faq.questions.q13.title": "Can I use geminigen.ai to create content for my website or blog?", "faq.questions.q13.content": "Yes, you can use our service to create audio content for your website, blog, or social media platforms, enriching the way you deliver information to your readers or customers.", "faq.questions.q14.title": "What is the output file format of the imagen?", "faq.questions.q14.content": "The primary output file format is png, which ensures compatibility with most devices.", "faq.questions.q15.title": "How much does it cost to generate a video?", "faq.questions.q15.content": "We calculate credits based on the number of seconds of the video you create. You can refer to detailed pricing on the page: https://geminigen.ai/pricing", "faq.questions.q16.title": "What is the output file format of the speech?", "faq.questions.q16.content": "The primary output file format is MP3,WAV, which ensures compatibility with most devices and music playback software.", "faq.questions.q17.title": "Is the payment process difficult? What payment methods are available?", "faq.questions.q17.content": "The payment process is very easy. We offer popular payment methods including PayPal, Debit, and Crypto", "faq.questions.q18.title": "Are there any subscription plans for frequent users?", "faq.questions.q18.content": "No, we sell credits without expiration date. When you run out of credits, buy another credit pack.", "faq.questions.q19.title": "Will I get a refund if my video fails or has an error?", "faq.questions.q19.content": "We will count credits when your video is successfully created. In case of errors, credits will not be counted.", "faq.contact.title": "Still have questions?", "faq.contact.description": "Our support team is ready to help you 24/7", "faq.contact.email": "Send Email", "faq.contact.discord": "Join <PERSON>", "footer.product.title": "Product", "footer.product.features": "Features", "footer.product.howto": "How to Use", "footer.product.pricing": "Pricing", "footer.product.app": "App", "footer.company.title": "Company", "footer.company.about": "About Us", "footer.company.blog": "Blog", "footer.company.careers": "Careers", "footer.company.contact": "Contact", "footer.legal.title": "Legal", "footer.legal.privacy": "Privacy Policy", "footer.legal.terms": "Terms of Service", "footer.legal.cookies": "<PERSON><PERSON>", "footer.support.title": "Support", "footer.support.help": "Help Center", "footer.support.api": "API Docs", "footer.support.status": "System Status", "footer.description": "Create high-quality AI content with the most advanced technology. Transform ideas into professional content in seconds.", "footer.newsletter.title": "Get Latest Updates", "footer.newsletter.description": "Subscribe to receive news about new features and product updates", "footer.newsletter.placeholder": "Enter your email", "footer.newsletter.subscribe": "Subscribe", "footer.copyright": "© 2024 GeminiGen AI. All rights reserved.", "footer.language": "English", "Features That Set Us Apart": "Features That Set Us Apart", "videoGen.imageToVideo": "Image-to-Video", "videoGen.textToVideo": "Text-to-Video", "videoGen.generated": "Generated", "videoGen.imageToVideoGenerated": "Image-to-Video Generated", "videoGen.textToVideoGenerated": "Text-to-Video Generated", "videoGen.generationStarted": "We are starting to generate your video, please wait about 2 minutes...", "videoGen.imageToVideoGenerationStarted": "We are starting to generate your video from image, please wait about 2 minutes...", "videoGen.textToVideoGenerationStarted": "We are starting to generate your video from text, please wait about 2 minutes...", "videoGen.pleaseEnterPrompt": "Please enter a prompt to generate a video.", "videoGen.cinematic": "Cinematic", "videoGen.model": "Model", "videoGen.imageReference": "Image Reference", "videoGen.prompt": "Prompt", "videoGen.negativePrompt": "Negative Prompt", "videoGen.negativePromptDescription": "Describe what you don't want to see in the video", "videoGen.negativePromptPlaceholder": "Enter what you want to exclude from the video...", "videoGen.negativePromptTooltip": "Negative prompts help exclude unwanted elements, styles, or concepts from your video generation. For example: 'blurry, low quality, distorted faces'", "videoGen.negativePromptSuggestions": "Quick suggestions", "videoGen.negativePromptSuggestion1": "blurry, low quality", "videoGen.negativePromptSuggestion2": "distorted faces", "videoGen.negativePromptSuggestion3": "bad anatomy", "videoGen.negativePromptSuggestion4": "watermark, text", "videoGen.negativePromptSuggestion5": "oversaturated", "videoGen.negativePromptSuggestion6": "unrealistic motion", "videoGen.enhancePrompt": "Enhance Prompt", "videoGen.enhancePromptOn": "Prompts will always be refined to improve output quality", "videoGen.enhancePromptOnRequired": "Prompts will always be refined to improve output quality (required for this model)", "videoGen.enhancePromptOff": "Prompts will not be modified", "videoGen.enhancePromptLocked": "This setting is locked for the selected model", "videoGen.enhancePromptNotRefined": "Prompts will not be refined", "videoGen.aspectRatio": "Aspect Ratio", "videoGen.duration": "Duration", "videoGen.selectDuration": "Select video duration in seconds", "videoGen.creditsRemaining": "Credits: {credits} remaining", "videoGen.generationCost": "This generation will cost: {cost} Credits for {duration}s", "videoGen.generateVideo": "Generate Video", "videoGen.somethingWentWrong": "Something went wrong", "videoGen.examplesTitle": "AI Video Generation Examples", "videoGen.examplesDescription": "Explore the power of AI video generation with these interactive comparisons", "videoGen.processing": "Processing your video...", "videoGen.analyzing": "Analyzing your input...", "videoGen.rendering": "Rendering your video...", "videoGen.finalizing": "Finalizing your video...", "videoGen.almostReady": "Your video is almost ready...", "videoGen.estimatedTime": "Estimated time: 2-3 minutes", "videoGen.backgroundProcessing": "Your video is being generated in the background", "videoGen.checkHistory": "You can check the progress in your history", "videoGen.willNotify": "We will notify you when it's ready", "videoGen.error.invalidPrompt": "Please enter a valid prompt", "videoGen.error.invalidModel": "Please select a valid model", "videoGen.error.invalidDuration": "Please select a valid duration", "videoGen.error.invalidAspectRatio": "Please select a valid aspect ratio", "videoGen.error.insufficientCredits": "Insufficient credits for video generation", "videoGen.error.fileTooLarge": "Selected image file is too large", "videoGen.error.invalidFileFormat": "Invalid image file format", "videoGen.error.generationFailed": "Video generation failed", "videoGen.error.networkError": "Network error during video generation", "videoGen.error.serverError": "Server error during video generation", "videoGen.success.generationStarted": "Video generation started successfully", "videoGen.success.generationCompleted": "Video generation completed successfully", "videoGen.success.imageUploaded": "Image uploaded successfully", "videoGen.success.promptSaved": "Prompt saved successfully", "Image Generation": "Image Generation", "We are starting to generate your image, please wait about 1 minute...": "We are starting to generate your image, please wait about 1 minute...", "We are starting to generate speech from your document, please check back later...": "We are starting to generate speech from your document, please check back later...", "We are starting to generate your speech, please wait about {time} {unit}...": "We are starting to generate your speech, please wait about {time} {unit}...", "minutes": "minutes", "seconds": "seconds", "Transform your ideas into stunning AI-generated images, videos, speech, and more.": "Transform your ideas into stunning AI-generated images, videos, speech, and more.", "Save up to {0} compared to traditional creative services while experiencing the future of content generation.": "Save up to {0} compared to traditional creative services while experiencing the future of content generation."}